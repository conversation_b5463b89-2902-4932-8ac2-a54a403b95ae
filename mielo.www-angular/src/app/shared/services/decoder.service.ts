import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { BASE_URL } from '../../core/config';
import {
    DecodeModel,
} from '../interfaces/decoder.interface';
import {
    CentriRicoveroModel,
    ComuneModel,
    DizionarioModel,
    NazioneModel,
    ProvinciaModel,
    UnitaOperativaModel
} from "../interfaces/scheda-ricovero.interface";
import { StruttureRiabilitativeModel } from "../interfaces/shared/shared.interface";

@Injectable({
    providedIn: 'root'
})
export class DecoderService {

    listStatoLesione: DecodeModel[] = [];
    listComune: ComuneModel[] = [];
    listComuniNascita: ComuneModel[] = [];
    listComuniResidenza: ComuneModel[] = [];
    listComuniDomicilio: ComuneModel[] = [];
    listProvince: ProvinciaModel[] = [];
    listNazione: NazioneModel[] = [];
    private statoLesioneReady = new BehaviorSubject<boolean>(false);
    statoLesioneReady$ = this.statoLesioneReady.asObservable();

    constructor(private httpClient: HttpClient) { }

    /**Decoder per stato lesione */
    getDcodStatoLesione() {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=STATO_LESIONE`).pipe(
            map(response => {
                this.listStatoLesione = response.map(item => ({
                    id: item.idDizionario,
                    codice: item.categoria,
                    descrizione: item.descrizione,
                    idDizionario: item.idDizionario,
                    categoria: item.categoria
                }));
                this.statoLesioneReady.next(true);
                return response;
            })
        );
    }

    /**Decoder per occupazione */
    getDcodOccupazione() {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=OCCUPAZIONE`);
    }

    /**Decoder per stato civile */
    getDcodStatoCivile() {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=STATO_CIVILE`);
    }
    /**Decoder per genere sessuale */
    getDcodGenereSessuale() {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=GENERE`);
    }

    /**Decoder per scolarità */
    getDcodScolarita() {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=SCOLARITA`);
    }

    /**Decoder per unità operative */
    getDcodUnitaOperative(): Observable<UnitaOperativaModel[]> {
        return this.httpClient.get<UnitaOperativaModel[]>(`${BASE_URL}/decode/unitaoperativa`)
    }

    /**Decoder per tipo codice identificativo */
    getDcodTipoCodIdentificativo() {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=TIPO_CODICE`)
    }

    /**Decoder per comuni */
    getDcodComuni(provincia: string, dataValidita: string, empty = false) {
        return this.httpClient.get<ComuneModel[]>(`${BASE_URL}/decode/comuni?provincia=${provincia}&dataValidita=${dataValidita}&empty=${empty}`)
    }

    /**Decoder per province */
    getDcodProvince(dataValidita: string, empty: boolean, statoEstero = false) {
        return this.httpClient.get<ProvinciaModel[]>(`${BASE_URL}/decode/province?dataValidita=${dataValidita}&empty=${empty}&provinciaNascita=${statoEstero}`)
    }

    /**Decoder per nazioni */
    getDcodNazioni(dataValidita: string, empty: boolean) {
        return this.httpClient.get<NazioneModel[]>(`${BASE_URL}/decode/nazioni?dataValidita=${dataValidita}&empty=${empty}`)
    }

    /**Decoder per nazioni INCLUSO ITALIA */
    getDcodNazioni_ITALIA(dataValidita: string) {
        return this.httpClient.get<NazioneModel[]>(`${BASE_URL}/decode/nazioni?dataValidita=${dataValidita}&cittadinanza=true`)
    }

    /**
     * Recupera i comuni già caricati per un tipo specifico (nascita, residenza, domicilio)
     */
    getComuniByProvincia(tipo: 'nascita' | 'residenza' | 'domicilio'): ComuneModel[] {
        if (tipo === 'nascita') {
            return this.listComuniNascita;
        } else if (tipo === 'residenza') {
            return this.listComuniResidenza;
        } else if (tipo === 'domicilio') {
            return this.listComuniDomicilio;
        }
        return [];
    }

    /**Decoder per BOOLEAN */
    getDcodBoolean(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=BOOLEAN`);
    }

    /**Decoder per NON TRAUMATICA */
    getDcodSiNoNonValutabile(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=SI_NO_INVALUTABILE`);
    }

    /**Decoder per COLLABORAZIONE_VAL_INGRESSO */
    getDcodCollaborazione(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=COLLABORAZIONE_VAL_INGRESSO`);
    }

    /**Decoder per TIPO_NON_TRAUMATICA_EZIOLOGIA */
    getDcodNonTraumatica(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=TIPO_NON_TRAUMATICA_EZIOLOGIA`);
    }

    /**Decoder per TIPO_TRAUMA */
    getDcodTipoTrauma(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=TIPO_TRAUMA`);
    }

    /**Decoder per SPORT */
    getDcodSport(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=SPORT`);
    }

    /**Decoder per TIPO_CADUTA */
    getDcodCaduta(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=TIPO_CADUTA`);
    }

    /**Decoder per VERTEBRE */
    getDcodVertebre(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=VERTEBRE`);
    }

    /**Decoder per LESIONE_VERTEBRALE */
    getDcodTipoLesioneVertebrale() {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=LESIONE_VERTEBRALE`);
    }

    /**Decoder per TRATTAMENTO_MEDICO_ACUTO */
    getDcodTrattamentoMedicoAcuto() {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=TRATTAMENTO_MEDICO_ACUTO`);
    }

    /**Decoder per TIPOLOGIA_INTERVENTO */
    getDcodTipologiaIntervento(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=TIPOLOGIA_INTERVENTO_LESIONE`);
    }

    /**Decoder per DIAGNOSTICA_LESIONE */
    getDcodDiagnostica(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=DIAGNOSTICA_LESIONE`);
    }

    /**Decoder per TIPO_TRAUMA_ASSOCIATO */
    getDcodTraumiAssociati(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=TIPO_TRAUMA_ASSOCIATO`);
    }

    /**Decoder per GESTIONE_ALVO */
    getDcodGestioneAlvo(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=GESTIONE_ALVO`);
    }

    /**Decoder per GESTIONE_VESCICALE */
    getDcodGestioneVescicale(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=GESTIONE_VESCICALE`);
    }

    /**Decoder per TRATTAMENTI_EFFETTUATI */
    getDcodTrattamentiEffettuati(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=TRATTAMENTI_EFFETTUATI`);
    }

    /**Decoder per RESPIRAZIONE */
    getDcodRespirazione(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=RESPIRAZIONE`);
    }

    /**Decoder per ALIMENTAZIONE */
    getDcodAlimentazione(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=ALIMENTAZIONE`);
    }

    /**Decoder per TIPO_MATERASSO */
    getDcodTipoMaterasso(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=TIPO_MATERASSO`);
    }

    /**Decoder per TRASFERIMENTO_SETTING_RIABILITATIVO */
    getDcodTrasferimentoSettingRiabilitativo(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=TRASFERIMENTO_SETTING_RIABILITATIVO`);
    }

    /**Decoder per STRUTTURE_LIVELLO2 */
    getDcodStrutturelivello2(): Observable<StruttureRiabilitativeModel[]> {
        return this.httpClient.get<StruttureRiabilitativeModel[]>(`${BASE_URL}/decode/strutturelivello2`).pipe(
            map(response => {
                console.log('Risposta API strutture livello 2:', response);
                if (!response || response.length === 0) {
                    console.warn('Nessuna struttura ricevuta dall\'API');
                }
                return response;
            })
        );
    }

    getDcodReparti(): Observable<CentriRicoveroModel[]> {
        return this.httpClient.get<CentriRicoveroModel[]>(`${BASE_URL}/decode/reparti`);
    }

    getDcodModalitaDimissioneSdo(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=MODALITA_DIMISSIONE_SDO`);
    }

    getDcodDestinazioneDimissione(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=DESTINAZIONE_DIMISSIONE`);
    }

    getDcodCondizioneDimissione(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=CONDIZIONE_DIMISSIONE`);
    }

    getDcodTipologiaRiabilitativa(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=TIPOLOGIA_RIABILITATIVA`);
    }

    getDcodProblematicheDimissioneProgrammata(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=PROBLEMATICHE_DIMISSIONE_PROGRAMMATA`);
    }

    /**Decoder per AUSILI_DIMISSIONE */
    getDcodAusiliMobility(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=AUSILI_DIMISSIONE`);
    }

    /**Decoder per AUSILI_URINARI_DIMISSIONE */
    getDcodAusiliGestione(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=AUSILI_URINARI_DIMISSIONE`);
    }

    /**Decoder per AUSILI_FUNZIONI_VITALI */
    getDcodAusiliFunzioniVitali(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=AUSILI_FUNZIONI_VITALI`);
    }

    /**Decoder per PRESIDI_POSTURALI */
    getDcodPresidi(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=PRESIDI_POSTURALI`);
    }

    /**
     * Decoder per SEDE_OSSIFICAZIONE
     * Restituisce l'elenco delle possibili sedi di ossificazione eterotopica
     */
    getDcodSedeOssificazione(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=SEDE_OSSIFICAZIONE`);
    }

    /**
     * Decoder per SEDE_LESIONE_PRESSIONE
     * Restituisce l'elenco delle possibili sedi di lesione da pressione
     */
    getDcodSedeLesionePressione(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=SEDE_LESIONE_PRESSIONE`);
    }

    /**
     * Decoder per SEDE_TROMBOSI
     * Restituisce l'elenco delle possibili sedi di trombosi venosa profonda
     */
    getDcodSedeTrombosi(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=SEDE_TROMBOSI`);
    }

    /**
     * Decoder per RACHIDE
     * Restituisce l'elenco delle possibili complicanze del rachide
     */
    getDcodRachide(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=RACHIDE`);
    }

    /**
     * Decoder per DOLORE_COMPLICANZE
     * Restituisce l'elenco delle possibili tipologie di dolore
     */
    getDcodDoloreComplicanze(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=DOLORE_COMPLICANZE`);
    }

    /**Decoder per TRATTAMENTI_SPASTICITA_DIMISSIONE */
    getDcodTrattamentiSpasticitaDimissione(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=TRATTAMENTI_SPASTICITA_DIMISSIONE`);
    }

    /**Decoder per TRATTAMENTI_SPASTICITA_DIMISSIONE */
    getDcodCaregiver(): Observable<DizionarioModel[]> {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=CAREGIVER`);
    }

    /**Decoder per complicanze respiratorie */
    getDcodComplicanzeRespiratorie() {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=COMPLICANZE_RESPIRATORIE`);
    }

    /**Decoder per complicanze urologiche */
    getDcodComplicanzeUrologiche() {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=COMPLICANZE_UROLOGICHE`);
    }

    /**Decoder per limitante e non */
    getDcodLimitanteENon() {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=LIMITANTE_NON_LIMITANTE`);
    }

    /**Decoder per presenti e non */
    getDcodPresentiENon() {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=PRESENTI_INTERCORSE`);
    }

    /**Decoder per presenti e non e per si/no */
    getDcodPresentiENonConSiNo() {
        return this.httpClient.get<DizionarioModel[]>(`${BASE_URL}/decode?cat=NO_PRESENTI_INTERCORSE`);
    }
}

