import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { ETipoScheda } from '../enums/enum';
import { OutputHistoryDTO } from '../interfaces/shared/shared.interface';
import { SchedaRicoveroService } from '../../mielo/services/scheda-ricovero.service';

export interface CronologiaMenuState {
  open: boolean;
  data: OutputHistoryDTO[];
  loading: boolean;
  title: string;
  position?: { top: number; left: number; right: number };
}

@Injectable({
  providedIn: 'root'
})
export class CronologiaMenuService {
  private cronologiaMenus = new BehaviorSubject<{ [key: string]: CronologiaMenuState }>({});
  
  public cronologiaMenus$ = this.cronologiaMenus.asObservable();

  constructor(private schedaRicoveroService: SchedaRicoveroService) {}

  /**
   * Apre un menu cronologia per il tipo scheda specificato
   */
  openCronologiaMenu(
    idPaziente: number, 
    idTipoScheda: ETipoScheda | null, 
    buttonElement?: HTMLElement
  ): Observable<OutputHistoryDTO> {
    const menuKey = this.getMenuKey(idTipoScheda);
    const title = this.getCronologiaTitle(idTipoScheda);
    
    // Chiudi tutti gli altri menu
    this.closeAllMenus();
    
    // Calcola posizione se fornito l'elemento bottone
    const position = buttonElement ? this.calculateMenuPosition(buttonElement) : undefined;
    
    // Inizializza/aggiorna lo stato del menu
    this.updateMenuState(menuKey, {
      open: true,
      loading: true,
      data: [],
      title: title,
      position: position
    });

    // Effettua la chiamata API
    return this.schedaRicoveroService.getCronologia({
      idPaziente: idPaziente,
      idTipoScheda: idTipoScheda
    }).pipe(
      tap(res => {
        const data = Array.isArray(res) ? res : [res];
        this.updateMenuState(menuKey, {
          open: true,
          loading: false,
          data: data,
          title: title,
          position: position
        });
      }),
      tap({
        error: (err) => {
          console.error('Errore nel caricamento cronologia:', err);
          this.updateMenuState(menuKey, {
            open: true,
            loading: false,
            data: [],
            title: title,
            position: position
          });
        }
      })
    );
  }

  /**
   * Chiude un menu specifico
   */
  closeCronologiaMenu(idTipoScheda: ETipoScheda | null): void {
    const menuKey = this.getMenuKey(idTipoScheda);
    const currentMenus = this.cronologiaMenus.value;
    
    if (currentMenus[menuKey]) {
      const updatedMenus = { ...currentMenus };
      delete updatedMenus[menuKey];
      this.cronologiaMenus.next(updatedMenus);
    }
  }

  /**
   * Chiude tutti i menu aperti
   */
  closeAllMenus(): void {
    this.cronologiaMenus.next({});
  }

  /**
   * Verifica se un menu è aperto
   */
  isMenuOpen(idTipoScheda: ETipoScheda | null): boolean {
    const menuKey = this.getMenuKey(idTipoScheda);
    return this.cronologiaMenus.value[menuKey]?.open || false;
  }

  /**
   * Ottiene lo stato di un menu
   */
  getMenuState(idTipoScheda: ETipoScheda | null): CronologiaMenuState | null {
    const menuKey = this.getMenuKey(idTipoScheda);
    return this.cronologiaMenus.value[menuKey] || null;
  }

  /**
   * Ottiene tutti i menu aperti
   */
  getOpenMenus(): { [key: string]: CronologiaMenuState } {
    return this.cronologiaMenus.value;
  }

  /**
   * Genera una chiave unica per il menu
   */
  private getMenuKey(idTipoScheda: ETipoScheda | null): string {
    return `cronologia_${idTipoScheda ?? 'dati_generali'}`;
  }

  /**
   * Ottiene il titolo del menu basato sul tipo scheda
   */
  private getCronologiaTitle(idTipoScheda: ETipoScheda | null): string {
    if (idTipoScheda === null) {
      return 'Cronologia Dati Generali';
    }

    switch (idTipoScheda) {
      case ETipoScheda.VALUTAZIONE_IN_INGRESSO:
        return 'Cronologia Valutazione in Ingresso';
      case ETipoScheda.NECESSITA_ASSISTENZIALI_IN_INGRESSO:
        return 'Cronologia Necessità Assistenziali';
      case ETipoScheda.QUADRO_NEUROLOGICO:
        return 'Cronologia Quadro Neurologico';
      case ETipoScheda.EZIOLOGIA:
        return 'Cronologia Eziologia';
      case ETipoScheda.LESIONE_E_TRATTAMENTO:
        return 'Cronologia Lesione e Trattamento';
      case ETipoScheda.SETTING_RIABILITATIVO:
        return 'Cronologia Setting Riabilitativo';
      case ETipoScheda.COMPLICANZE:
        return 'Cronologia Complicanze';
      default:
        return 'Cronologia';
    }
  }

  /**
   * Calcola la posizione del menu basata sull'elemento bottone
   */
  private calculateMenuPosition(buttonElement: HTMLElement): { top: number; left: number; right: number } {
    const rect = buttonElement.getBoundingClientRect();
    return {
      top: rect.bottom + window.scrollY,
      left: rect.left + window.scrollX,
      right: rect.right + window.scrollX
    };
  }

  /**
   * Aggiorna lo stato di un menu specifico
   */
  private updateMenuState(menuKey: string, state: CronologiaMenuState): void {
    const currentMenus = this.cronologiaMenus.value;
    this.cronologiaMenus.next({
      ...currentMenus,
      [menuKey]: state
    });
  }
}
