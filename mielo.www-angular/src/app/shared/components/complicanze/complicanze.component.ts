import { CommonModule } from "@angular/common"
import { Component, Input, OnDestroy, OnInit } from '@angular/core'
import { AbstractControl, FormArray, FormBuilder, FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms'
import { MatButtonModule } from "@angular/material/button"
import { MatCheckboxModule } from "@angular/material/checkbox"
import { MatExpansionModule } from "@angular/material/expansion"
import { MatFormFieldModule } from "@angular/material/form-field"
import { MatInputModule } from "@angular/material/input"
import { MatRadioModule } from "@angular/material/radio"
import { MatSelectModule } from "@angular/material/select"
import { DatiCliniciService } from "../../../mielo/services/dati-clinici.service"
import { DatiDimissioneService } from "../../../mielo/services/dati-dimissione.service"
import { TipoRicoveroEnum } from "../../enums/tipo-ricovero.enum"
import { DizionarioModel } from "../../interfaces/scheda-ricovero.interface"
import { debounceTime, distinctUntilChanged, forkJoin, Observable, Subscription, tap } from "rxjs";
import { DecoderService } from "../../services/decoder.service";
import { EComplicanzeAltroNessuna, ERROR_MESSAGE } from "../../enums/enum";
import { MatTooltip } from "@angular/material/tooltip";
import {
  CheckBoxDictionaryModel,
  CheckBoxNumeroModel,
  CheckBoxSempliceModel
} from "../../interfaces/shared/shared.interface"
import { CapitalizePipe } from "../../pipes/capitalize.pipe"
import { ModalService } from "../../services/modal.service"
import { toggleRadioSelection } from "../../utils/utils"
import { SchedaComplicanzeModel } from "../../interfaces/dati-clinici.interface"
import { SchedaComplicanzeDimissioneModel } from "../../interfaces/dati-dimissione.interface"

@Component({
  selector: 'app-complicanze',
  templateUrl: './complicanze.component.html',
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatExpansionModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatRadioModule,
    MatCheckboxModule,
    MatSelectModule,
    MatTooltip,
    CapitalizePipe,
  ],
  standalone: true,
  styleUrls: ['./complicanze.component.scss']
})
export class ComplicanzeComponent implements OnInit, OnDestroy {

  @Input() repartoType!: TipoRicoveroEnum
  @Input() isDimissione: boolean = false;
  @Input() readOnly: boolean = false;

  complicanzeForm: FormGroup
  optionsSedeOssificazione: DizionarioModel[] = [];
  optionsSedeLesionePressione: DizionarioModel[] = [];
  optionsSedeTrombosi: DizionarioModel[] = [];
  optionsComplicanzeRespiratorie: DizionarioModel[] = [];
  optionsComplicanzeUrologiche: DizionarioModel[] = [];
  optionsRachide: DizionarioModel[] = [];
  optionsDoloreComplicanze: DizionarioModel[] = [];
  optionsLimitante: DizionarioModel[] = [];
  optionsPresentiIntercorse: DizionarioModel[] = [];
  optionsPresentiIntercorseSiNo: { presenti: DizionarioModel[], siNo: DizionarioModel[] };
  optionsStadio: number[] = [];
  optionsSpasticita: number[] = [];
  loadDecoders$: Subscription;
  subscriptions: Subscription[];

  keysPresentiIntercorseSiNokeys = [
    'ossificazioneEterotopiche',
    'lesioniPressione',
    'osteomielite',
    'trombosiVenosaProfonda',
    'emboliaPolmonare',
    'spasticita',
    'dolore',
    'infezioneContaminazioneMultiresistente',
    'sepsi',
  ];

  complicanzeRespiratorieRequired: boolean = false;
  complicanzeUrologicheRequired: boolean = false;
  rachideRequired: boolean = false;
  emboliaPolmonareRequired: boolean = false;
  doloreRequired: boolean = false;
  sepsiRequired: boolean = false;
  infezioneContaminazioneMultiresistenteRequired: boolean = false;
  ossificazioneEterotopicheRequired: boolean = false;
  lesioniPressioneRequired: boolean = false;
  trombosiVenosaProfondaRequired: boolean = false;
  osteomieliteRequired: boolean = false;
  spasticitaRequired: boolean = false;

  protected readonly EComplicanzeAltroNessuna = EComplicanzeAltroNessuna;
  protected readonly toggleRadioSelection = toggleRadioSelection;
  protected readonly ERROR_MESSAGE = ERROR_MESSAGE;


  constructor(private fb: FormBuilder,
    private modalService: ModalService,
    private decoderService: DecoderService,
    public datiCliniciService: DatiCliniciService,
    public datiDimissioneService: DatiDimissioneService,
  ) {
  }

  get formGroup(): FormGroup {
    return this.complicanzeForm;
  }

  ngOnInit(): void {
    this.complicanzeForm = this.isDimissione
      ? this.createComplicanzeDimissioneForm()
      : this.createComplicanzeDatiCliniForm();


    if (this.repartoType !== TipoRicoveroEnum.ACUTI) {
      this.complicanzeRespiratorieRequired = true;
      this.complicanzeUrologicheRequired = true;
      this.rachideRequired = true;
      this.emboliaPolmonareRequired = true;
      this.doloreRequired = true;
      this.sepsiRequired = true;
      this.infezioneContaminazioneMultiresistenteRequired = true;
      this.ossificazioneEterotopicheRequired = true;
      this.lesioniPressioneRequired = true;
      this.trombosiVenosaProfondaRequired = true;
      this.osteomieliteRequired = true;
      this.spasticitaRequired = true;

      this.keysPresentiIntercorseSiNokeys.forEach(field => {
        const controlName = this.isDimissione ? field : `${field}Selected`;
        const control = this.complicanzeForm.get(controlName);
        control?.setValidators(Validators.required);
        control?.updateValueAndValidity();
      });
    }


    this.initFormOptions();
  }

  createComplicanzeDimissioneForm(): FormGroup {
    return this.fb.group({
      altroComplicanzeRespiratorie: [{ value: undefined, disabled: true }],
      altroComplicanzeUrologiche: [{ value: undefined, disabled: true }],
      altroRachide: [{ value: undefined, disabled: true }],
      altroSedeOssificazione: [{ value: undefined, disabled: true }],
      altroSedePressione: [{ value: undefined, disabled: true }],
      complicanzeRespiratorieDimissioneLista: this.fb.array([]),
      complicanzeUrologicheDimissioneLista: this.fb.array([]),
      dolore: undefined,
      doloreDimissioneLista: this.fb.array([]),
      emboliaPolmonare: undefined,
      idScheda: [{ value: undefined, disabled: true }],
      infezioneContaminazioneMultiresistente: undefined,
      lesioniPressione: [{ value: undefined }],
      lesioniPressioneLista: this.fb.array([]),
      noteComplicanzeRespiratorie: undefined,
      noteComplicanzeUrologiche: [{ value: undefined, disabled: true }],
      noteDolore: undefined,
      noteEmbolia: [{ value: undefined, disabled: true }],
      noteMultiresistente: [{ value: undefined, disabled: true }],
      noteOssificazione: [{ value: undefined, disabled: true }],
      noteOsteomielite: [{ value: undefined, disabled: true }],
      notePressione: [{ value: undefined, disabled: true }],
      noteRachide: [{ value: undefined, disabled: true }],
      noteSepsi: [{ value: undefined, disabled: true }],
      noteTrombosi: [{ value: undefined, disabled: true }],
      ossificazioneDimissioneLista: this.fb.array([]),
      ossificazioneEterotopiche: undefined,
      osteomielite: undefined,
      rachideDimissioneLista: this.fb.array([]),
      sepsi: undefined,
      trombosiDimissioneLista: this.fb.array([]),
      trombosiVenosaProfonda: undefined,
    });
  }

  createComplicanzeDatiCliniForm(): FormGroup {
    const form = this.fb.group({
      nomeScheda: [''],
      idScheda: [{ value: null, disabled: true }],

      ossificazioneEterotopicheSelected: undefined,
      ossificazioneEterotopiche: undefined,
      ossificazioneLista: this.fb.array([]),
      altroSedeOssificazione: [{ value: undefined, disabled: true }],
      noteOssificazione: [{ value: undefined, disabled: true }],

      lesioniPressioneSelected: undefined,
      lesioniPressione: undefined,
      lesioniPressioneLista: this.fb.array([]),
      altroSedePressione: [{ value: undefined, disabled: true }],
      notePressione: [{ value: undefined, disabled: true }],

      osteomieliteSelected: undefined,
      osteomielite: undefined,
      noteOsteomielite: [{ value: undefined, disabled: true }],

      trombosiVenosaProfondaSelected: undefined,
      trombosiVenosaProfonda: undefined,
      trombosiLista: this.fb.array([]),
      noteTrombosi: [{ value: undefined, disabled: true }],

      emboliaPolmonareSelected: undefined,
      emboliaPolmonare: undefined,
      noteEmbolia: [{ value: undefined, disabled: true }],

      complicanzeRespiratorie: this.fb.array([]),
      altroComplicanzeRespiratorie: [{ value: undefined, disabled: true }],
      noteComplicanzeRespiratorie: undefined,

      complicanzeUrologiche: this.fb.array([]),
      altroComplicanzeUrologiche: [{ value: undefined, disabled: true }],
      noteComplicanzeUrologiche: [{ value: undefined, disabled: true }],

      rachideLista: this.fb.array([]),
      altroRachide: [{ value: undefined, disabled: true }],
      noteRachide: [{ value: undefined, disabled: true }],

      spasticitaSelected: undefined,
      spasticita: undefined,
      punteggioScalaAshworth: [{ value: undefined, disabled: true }],
      noteSpasticita: undefined,

      doloreSelected: undefined,
      doloreLista: this.fb.array([]),
      doloreBoolean: undefined,
      noteDolore: [{ value: undefined, disabled: true }],

      infezioneContaminazioneMultiresistenteSelected: undefined,
      infezioneContaminazioneMultiresistente: [{ value: undefined, disabled: true }],
      noteMultiresistente: [{ value: undefined, disabled: true }],

      sepsiSelected: undefined,
      sepsi: [{ value: undefined, disabled: true }],
      noteSepsi: [{ value: undefined, disabled: true }],
    });
    form.get('noteDolore')?.disable();
    return form;
  }

  initFormOptions() {
    // Creo array per gli stadi delle lesioni da pressione (1-4)
    this.optionsStadio = [1, 2, 3, 4];
    this.optionsSpasticita = [0, 1, 2, 3, 4];

    if (!this.isDimissione) {
      this.defaultDisablePresenteENon();
    }

    if (this.repartoType !== TipoRicoveroEnum.ACUTI) {
      this.complicanzeForm.get('complicanzeRespiratorie')?.setValidators(Validators.required);
      this.complicanzeForm.get('complicanzeUrologiche')?.setValidators(Validators.required);
      this.complicanzeForm.get('rachide')?.setValidators(Validators.required);
      this.complicanzeForm.get('dolore')?.setValidators(Validators.required);
      this.complicanzeForm.get('sepsi')?.setValidators(Validators.required);
      this.complicanzeForm.get('rachide')?.setValidators(Validators.required);
      this.complicanzeForm.get('infezioneContaminazioneMultiresistente')?.setValidators(Validators.required);
      this.complicanzeForm.get('ossificazioneEterotopiche')?.setValidators(Validators.required);
      this.complicanzeForm.get('lesioniPressione')?.setValidators(Validators.required);
      this.complicanzeForm.get('trombosiVenosaProfonda')?.setValidators(Validators.required);
      this.complicanzeForm.get('osteomelite')?.setValidators(Validators.required);
    }

    const decoders: Record<string, Observable<any>> = {
      sedeOssificazione: this.decoderService.getDcodSedeOssificazione(),
      sedeLesionePressione: this.decoderService.getDcodSedeLesionePressione(),
      sedeTrombosi: this.decoderService.getDcodSedeTrombosi(),
      complicanzeRespiratorie: this.decoderService.getDcodComplicanzeRespiratorie(),
      complicanzeUrologiche: this.decoderService.getDcodComplicanzeUrologiche(),
      rachide: this.decoderService.getDcodRachide(),
      doloreComplicanze: this.decoderService.getDcodDoloreComplicanze(),
      limitante: this.decoderService.getDcodLimitanteENon()
    };

    if (!this.isDimissione) {
      decoders['presenti'] = this.decoderService.getDcodPresentiENon();
      decoders['presentiSiNo'] = this.decoderService.getDcodPresentiENonConSiNo();
    }

    this.loadDecoders$ = forkJoin(decoders).pipe(
      tap(options => {
        this.optionsSedeOssificazione = options['sedeOssificazione']
        this.optionsSedeLesionePressione = options['sedeLesionePressione'];
        this.optionsSedeTrombosi = options['sedeTrombosi'];
        this.optionsComplicanzeRespiratorie = options['complicanzeRespiratorie'];
        this.optionsComplicanzeUrologiche = options['complicanzeUrologiche'];
        this.optionsRachide = options['rachide'];
        this.optionsDoloreComplicanze = options['doloreComplicanze'];
        this.optionsLimitante = options['limitante'];
        if (!this.isDimissione) {
          this.optionsPresentiIntercorse = options['presenti'];
          this.optionsPresentiIntercorseSiNo = {
            presenti: options['presentiSiNo'].filter((o: DizionarioModel) => o.idDizionario === 249 || o.idDizionario === 250),
            siNo: options['presentiSiNo'].filter((o: DizionarioModel) => o.idDizionario === 248 || o.idDizionario === 411)
          }
        }

        this.createOssificazFormArray()

        this.createLesioniPressioneFormArray()

        this.createTrombosiFormArray()

        this.createComplicanzeRespiratorieFormArray()

        this.createComplicanzeUrologicheFormArray()

        this.createRachideFormArray()

        this.createDoloreFormArray()

        let dati;
        if (!this.isDimissione) {
          dati = this.datiCliniciService.getDatiComplicanzeValue();
        } else {
          dati = this.datiDimissioneService.getComplicanzeValue();
        }
        if (dati) {
          this.populateForm(dati)
        }

        if (this.readOnly) {
          Object.keys(this.complicanzeForm.controls).forEach(controlName => {
            if (!controlName.toLowerCase().includes('note') || !controlName.toLowerCase().includes('nota')) {
              this.complicanzeForm.get(controlName)?.disable({ emitEvent: false });
            }
          });
        } else {
          this.subscribeToControlsChanges();

          const formChanges$ = this.complicanzeForm.valueChanges.pipe(
            distinctUntilChanged((a, b) => JSON.stringify(a) === JSON.stringify(b)),
            debounceTime(1)
          ).subscribe(() => {
            const formValue = this.complicanzeForm.getRawValue();

            let payload: SchedaComplicanzeModel | SchedaComplicanzeDimissioneModel;

            if (!this.isDimissione) {
              payload = {
                ...this.formatFormValues(formValue),
                ossificazioneLista: this.getSelectedItems(this.ossificazioneArray),
                lesioniPressioneLista: this.getSelectedItems(this.lesioniPressioneArray),
                trombosiLista: this.getSelectedItems(this.trombosiArray),
                complicanzeRespiratorie: this.getSelectedItems(this.complicanzeRespiratorieArray),
                complicanzeUrologiche: this.getSelectedItems(this.complicanzeUrologicheArray),
                rachideLista: this.getSelectedItems(this.rachideArray),
                doloreLista: this.getSelectedItems(this.doloreArray)
              };

              this.datiCliniciService.setDatiComplicanze(payload as SchedaComplicanzeModel);
              this.datiCliniciService.setDatiComplicanzeValido(this.complicanzeForm.valid);
            } else {
              payload = {
                ...formValue,
                ossificazioneDimissioneLista: this.getSelectedItems(this.ossificazioneArray),
                lesioniPressioneLista: this.getSelectedItems(this.lesioniPressioneArray),
                trombosiDimissioneLista: this.getSelectedItems(this.trombosiArray),
                complicanzeRespiratorieDimissioneLista: this.getSelectedItems(this.complicanzeRespiratorieArray),
                complicanzeUrologicheDimissioneLista: this.getSelectedItems(this.complicanzeUrologicheArray),
                rachideDimissioneLista: this.getSelectedItems(this.rachideArray),
                doloreDimissioneLista: this.getSelectedItems(this.doloreArray)
              };

              this.datiDimissioneService.setComplicanze(payload as SchedaComplicanzeDimissioneModel);
              this.datiDimissioneService.setComplicanzeValido(this.complicanzeForm.valid);
            }
          })

          this.subscriptions?.push(formChanges$);
        }
      })
    ).subscribe()
  }

  formatFormValues(formValue: any): any {
    const formattedValues = { ...formValue };

    this.keysPresentiIntercorseSiNokeys.forEach((key) => {
      const selectedKey = `${key}Selected`;
      const selectedValue = formValue[selectedKey];
      if (selectedValue?.selected) {
        formattedValues[key] = selectedValue;
      }

      delete formattedValues[selectedKey];
    });

    return formattedValues;
  }

  populateForm(dati: any) {
    this.patchIdsArray(dati.trombosiDimissioneLista || dati.trombosiLista, this.trombosiArray);
    this.patchIdsArray(dati.complicanzeRespiratorieDimissioneLista || dati.complicanzeRespiratorie, this.complicanzeRespiratorieArray);
    this.patchIdsArray(dati.complicanzeUrologicheDimissioneLista || dati.complicanzeUrologiche, this.complicanzeUrologicheArray);

    if (this.isDimissione) {
      this.patchIdsArray(dati.doloreDimissioneLista, this.doloreArray);
      this.patchIdsArray(dati.rachideDimissioneLista, this.rachideArray);
      this.populateArrayIdDizionario(dati.complicanzeRespiratorieDimissioneLista, this.complicanzeRespiratorieArray);
      this.populateArrayIdDizionario(dati.complicanzeUrologicheDimissioneLista, this.complicanzeUrologicheArray);
    } else {
      this.populateDoloreArray(dati.doloreLista);
      this.populateRachideArray(dati.rachideLista);
      this.populateComplicanzeUrologicheArray(dati.complicanzeRespiratorie);
      this.populateComplicanzeRespiratorieArray(dati.complicanzeUrologiche);
      this.populateArrayIdDizionario(dati.trombosiLista, this.trombosiArray);
      this.populateArrayIdDizionario(dati.rachideLista, this.rachideArray);
    }

    this.populateOssificazioneArray(this.isDimissione ? dati.ossificazioneDimissioneLista : dati.ossificazioneLista);
    this.populateLesioniPress(dati.lesioniPressioneLista);

    const commonConfig: Array<{ key: string; enableOnValue?: boolean }> = [
      { key: 'ossificazioneEterotopiche', enableOnValue: true },
      { key: 'lesioniPressione', enableOnValue: true },
      { key: 'osteomielite', enableOnValue: true },
      { key: 'trombosiVenosaProfonda', enableOnValue: true },
      { key: 'emboliaPolmonare', enableOnValue: true },
      { key: 'infezioneContaminazioneMultiresistente', enableOnValue: true },
      { key: 'sepsi', enableOnValue: true },
      { key: 'dolore', enableOnValue: true },

      { key: 'noteOssificazione' },
      { key: 'notePressione' },
      { key: 'noteOsteomielite' },
      { key: 'noteTrombosi' },
      { key: 'noteEmbolia' },
      { key: 'noteMultiresistente' },
      { key: 'noteSepsi' },
      { key: 'noteDolore' },

      { key: 'altroSedeOssificazione' },
      { key: 'altroSedePressione' },
      { key: 'altroComplicanzeRespiratorie' },
      { key: 'altroComplicanzeUrologiche' },
      { key: 'altroRachide' },
    ];

    const datiCliniciOnly: Array<{ key: string; enableOnValue?: boolean }> = [
      { key: 'rachide', enableOnValue: true },
      { key: 'noteRachide' },
      { key: 'spasticita' },
      { key: 'punteggioScalaAshworth' },
      { key: 'noteSpasticita' },
    ];

    const finalConfig = this.isDimissione
      ? commonConfig
      : [...commonConfig, ...datiCliniciOnly];

    finalConfig.forEach(cfg => {
      const value = (dati as any)[cfg.key];
      if (cfg.enableOnValue) {
        this.patchControl(cfg.key, value, { enableOnValue: true });
      } else {
        this.patchControl(cfg.key, value);
      }
    });

    if (!this.isDimissione) {
      this.keysPresentiIntercorseSiNokeys.forEach(key => {
        const value: DizionarioModel = (dati as any)[key];
        const selectedControl = this.complicanzeForm.get(`${key}Selected`);
        const mainControl = this.complicanzeForm.get(key);

        if (value?.idDizionario === 248 || value?.idDizionario === 411) {
          selectedControl?.setValue(
            this.optionsPresentiIntercorseSiNo?.siNo?.find(o => o.idDizionario === value.idDizionario) ?? null
          );
          this.toggleArrayStatus(key, value);
        } else if (value) {
          mainControl?.setValue(value);
          selectedControl?.setValue(
            this.optionsPresentiIntercorseSiNo?.siNo?.find(o => o.idDizionario === 248) ?? null
          );
        }
      });
      this.complicanzeForm.updateValueAndValidity();
    }
  }

  defaultDisablePresenteENon() {
    Object.keys(this.complicanzeForm.controls).forEach(key => {
      if (key.endsWith('Selected')) {
        const disableControlKey = key.replace('Selected', '');
        this.complicanzeForm.get(disableControlKey)?.disable();
      }
    });
  }

  /** Set a scalar control, optionally enabling it if `value != null`. */
  private patchControl(
    path: string,
    value: any,
    opts: { enableOnValue?: boolean } = {}
  ) {
    const ctrl = this.complicanzeForm.get(path);
    if (!ctrl) return;
    if (opts.enableOnValue && value != null) {
      ctrl.enable({ emitEvent: false });
    }
    ctrl.setValue(value, { emitEvent: false });
  }

  /**
   * For a FormGroup `{ id, label }` bound to a DizionarioModel:
   */
  private patchDictionaryGroup(
    path: string,
    dict?: DizionarioModel
  ) {
    if (!dict) return;
    this.patchControl(`${path}.id`, dict.idDizionario, { enableOnValue: true });
    this.patchControl(`${path}.label`, dict.descrizione, { enableOnValue: true });
  }

  /** Populate a FormArray of simple booleans from an array of ids */
  private patchIdsArray(
    source: number[] | { idDizionario: number }[],
    array: FormArray,
    keyField: 'idDizionario' | 'key' = 'idDizionario'
  ) {
    // if your source is DTO objects, map to array of ids:
    const ids = (source as any[]).map(item =>
      typeof item === 'number' ? item : item[keyField]
    );
    this.populateArrayIdDizionario(ids, array);
  }

  populateArrayIdDizionario(data: CheckBoxSempliceModel[], formArray: FormArray) {
    formArray.controls.forEach((item, index) => {
      const selected = data.find(d => d.idDizionario === item.get('idDizionario')?.value);
      if (selected) {
        item.patchValue({
          selected: true
        });
        item.get('selected')?.enable()
      }
    });
  }

  populateOssificazioneArray(ossificazList: CheckBoxDictionaryModel[]) {
    this.ossificazioneArray.controls.forEach((item, index) => {
      const ossificazione = ossificazList.find(o => o.idDizionario1 === item.get('idDizionario1')?.value);
      if (ossificazione) {
        item.patchValue({
          idDizionario2: ossificazione.idDizionario2,
          selected: true
        });
        item.get('idDizionario2')?.enable()
        item.get('selected')?.enable()
      }
    });
  }

  populateDoloreArray(doloreList: CheckBoxDictionaryModel[]) {
    this.doloreArray.controls.forEach((item, index) => {
      const dolore = doloreList.find(o => o.idDizionario1 === item.get('idDizionario1')?.value);
      if (dolore) {
        item.patchValue({
          idDizionario2: dolore.idDizionario2,
          selected: true
        });
        item.get('idDizionario2')?.enable()
        item.get('selected')?.enable()
      }
    });
  }

  populateRachideArray(rachideList: CheckBoxDictionaryModel[]) {
    this.doloreArray.controls.forEach((item, index) => {
      const rachide = rachideList.find(o => o.idDizionario1 === item.get('idDizionario1')?.value);
      if (rachide) {
        item.patchValue({
          idDizionario2: rachide.idDizionario2,
          selected: true
        });
        item.get('idDizionario2')?.enable()
        item.get('selected')?.enable()
      }
    });
  }

  populateComplicanzeUrologicheArray(complicanzeList: CheckBoxDictionaryModel[]) {
    this.complicanzeUrologicheArray.controls.forEach((item, index) => {
      const complicanze = complicanzeList.find(o => o.idDizionario1 === item.get('idDizionario1')?.value);
      if (complicanze) {
        item.patchValue({
          idDizionario2: complicanze.idDizionario2,
          selected: true
        });
        item.get('idDizionario2')?.enable()
        item.get('selected')?.enable()
      }
    });
  }

  populateComplicanzeRespiratorieArray(complicanzeList: CheckBoxDictionaryModel[]) {
    this.complicanzeRespiratorieArray.controls.forEach((item, index) => {
      const complicanze = complicanzeList.find(o => o.idDizionario1 === item.get('idDizionario1')?.value);
      if (complicanze) {
        item.patchValue({
          idDizionario2: complicanze.idDizionario2,
          selected: true
        });
        item.get('idDizionario2')?.enable()
        item.get('selected')?.enable()
      }
    });
  }

  populateLesioniPress(lesioni: CheckBoxNumeroModel[]) {
    this.lesioniPressioneArray.controls.forEach((item, index) => {
      const lesione = lesioni.find(l => l.idDizionario === item.get('idDizionario')?.value);
      if (lesione) {
        item.patchValue({
          selected: true,
          numero: lesione.numero
        });
        item.get('selected')?.enable()
        item.get('numero')?.enable()
      }
    });
  }

  subscribeToControlsChanges(): void {
    const ossificazioneEterotopiche = this.isDimissione ? 'ossificazioneEterotopiche' : 'ossificazioneEterotopicheSelected';
    const lesioniPressione = this.isDimissione ? 'lesioniPressione' : 'lesioniPressioneSelected';
    const osteomielite = this.isDimissione ? 'osteomielite' : 'osteomieliteSelected';
    const infezioneContaminazioneMultiresistente = this.isDimissione ? 'infezioneContaminazioneMultiresistente' : 'infezioneContaminazioneMultiresistenteSelected';
    const trombosiVenosaProfonda = this.isDimissione ? 'trombosiVenosaProfonda' : 'trombosiVenosaProfondaSelected';
    const emboliaPolmonare = this.isDimissione ? 'emboliaPolmonare' : 'emboliaPolmonareSelected';
    const spasticita = this.isDimissione ? 'spasticita' : 'spasticitaSelected';
    const dolore = this.isDimissione ? 'dolore' : 'doloreSelected';
    const sepsi = this.isDimissione ? 'sepsi' : 'sepsiSelected';

    const osteomielite$ = this.complicanzeForm.get(osteomielite)?.valueChanges
      .subscribe(value => {
        if (value.idDizionario === 411 || (typeof value === 'boolean' && value)) {
          if (this.complicanzeForm.controls['osteomieliteSelected']) this.complicanzeForm.get('osteomielite')?.enable();
          this.complicanzeForm.get('noteOsteomielite')?.enable();
        } else {
          if (this.complicanzeForm.controls['osteomieliteSelected']) this.complicanzeForm.get('osteomielite')?.disable();
          this.complicanzeForm.get('noteOsteomielite')?.disable();
          this.complicanzeForm.get('noteOsteomielite')?.reset();
        }
        this.complicanzeForm.updateValueAndValidity()
      }) as Subscription;

    const ossificazioneEterotopiche$ = this.complicanzeForm.get(ossificazioneEterotopiche)?.valueChanges
      .subscribe(value => {
        // se seleziono si con il dizionario presenti/nopresenti oppure seleziono si con il dizionario si/no
        if (value?.idDizionario === 411 || (typeof value === 'boolean' && value)) {
          // se ci sono i radio presenti/intercorse
          if (this.complicanzeForm.controls['ossificazioneEterotopicheSelected']) this.complicanzeForm.get('ossificazioneEterotopiche')?.enable();
          this.complicanzeForm.get('noteOssificazione')?.enable();
          this.ossificazioneArray.controls.forEach((c, index) => {
            const selected = c.get('selected');
            if (selected instanceof FormControl) {
              selected.enable();
            } else {
              console.warn(`Unexpected control at ossificazioneArray[${index}]`, selected);
            }
          });
        } else {
          if (this.complicanzeForm.controls['ossificazioneEterotopicheSelected']) {
            this.complicanzeForm.get('ossificazioneEterotopiche')?.disable();
            this.complicanzeForm.controls['ossificazioneEterotopiche'].setValue(null, { emitEvent: false });
          }
          this.complicanzeForm.get('noteOssificazione')?.disable();
          this.complicanzeForm.get('noteOssificazione')?.reset();
          this.createOssificazFormArray();
        }
        this.complicanzeForm.updateValueAndValidity()
      }) as Subscription;

    const lesioniDaPressione$ = this.complicanzeForm.get(lesioniPressione)?.valueChanges
      .subscribe(value => {
        // se seleziono si con il dizionario presenti/nopresenti oppure seleziono si con il dizionario si/no
        if (value.idDizionario === 411 || (typeof value === 'boolean' && value)) {
          if (this.complicanzeForm.controls['lesioniPressioneSelected']) this.complicanzeForm.get('lesioniPressione')?.enable();
          this.complicanzeForm.get('notePressione')?.enable();
          this.lesioniPressioneArray.controls.forEach((c, index) => {
            const selected = c.get('selected');
            if (selected instanceof FormControl) {
              selected.enable();
            } else {
              console.warn(`Unexpected control at lesioniPressioneArray[${index}]`, selected);
            }
          });
        } else {
          if (this.complicanzeForm.controls['lesioniPressioneSelected']) {
            this.complicanzeForm.get('lesioniPressione')?.disable();
            this.complicanzeForm.get('lesioniPressioneSelected')?.setValue(null)
          }
          this.complicanzeForm.get('notePressione')?.disable();
          this.complicanzeForm.get('notePressione')?.reset();
          this.complicanzeForm.get('altroSedePressione')?.reset();
          this.complicanzeForm.get('altroSedePressione')?.disable();
          this.createLesioniPressioneFormArray();
        }
        this.complicanzeForm.updateValueAndValidity()
      }) as Subscription;

    const emboliaPolmonare$ = this.complicanzeForm.get(emboliaPolmonare)?.valueChanges
      .subscribe(value => {
        if (value.idDizionario === 411 || (typeof value === 'boolean' && value)) {
          if (this.complicanzeForm.controls['emboliaPolmonareSelected']) this.complicanzeForm.get('emboliaPolmonare')?.enable();
          this.complicanzeForm.get('noteEmbolia')?.enable();
        }
        else {
          if (this.complicanzeForm.controls['emboliaPolmonareSelected']) this.complicanzeForm.get('emboliaPolmonare')?.disable();
          this.complicanzeForm.get('emboliaPolmonare')?.reset();
          this.complicanzeForm.get('noteEmbolia')?.disable();
          this.complicanzeForm.get('noteEmbolia')?.reset();
        }
        this.complicanzeForm.updateValueAndValidity()
      }) as Subscription;

    const spasticita$ = this.complicanzeForm.get(spasticita)?.valueChanges
      .subscribe(value => {
        const statoControl = this.complicanzeForm.get('spasticita');
        const punteggioControl = this.complicanzeForm.get('punteggioAshworth');
        if (value === true) {
          statoControl?.enable();
          this.complicanzeForm.get('punteggioScalaAshworth')?.enable();
          this.complicanzeForm.get('noteSpasticita')?.enable();
          statoControl?.setValidators([Validators.required]);
          punteggioControl?.setValidators([Validators.required]);
        } else {
          statoControl?.disable();
          this.complicanzeForm.get('punteggioScalaAshworth')?.disable();
          this.complicanzeForm.get('noteSpasticita')?.disable();
          statoControl?.clearValidators();
          punteggioControl?.clearValidators();
          statoControl?.setValue('');
          punteggioControl?.setValue('');
        }
        statoControl?.updateValueAndValidity();
        punteggioControl?.updateValueAndValidity();
      }) as Subscription;

    const trombosiVenosaProfonda$ = this.complicanzeForm.get(trombosiVenosaProfonda)?.valueChanges
      .subscribe(value => {
        if (value.idDizionario === 411 || (typeof value === 'boolean' && value)) {
          if (this.complicanzeForm.controls['trombosiVenosaProfondaSelected']) this.complicanzeForm.get('trombosiVenosaProfonda')?.enable();
          this.complicanzeForm.get('noteTrombosi')?.enable();
          this.trombosiArray.controls.forEach((c, index) => {
            const selected = c.get('selected');
            if (selected instanceof FormControl) {
              selected.enable();
            } else {
              console.warn(`Unexpected control at trombosiArray[${index}]`, selected);
            }
          });
        } else {
          if (this.complicanzeForm.controls['trombosiVenosaProfondaSelected']) this.complicanzeForm.get('trombosiVenosaProfonda')?.disable();
          this.complicanzeForm.get('noteTrombosi')?.disable();
          this.complicanzeForm.get('noteTrombosi')?.reset();
          this.createTrombosiFormArray();
        }
        this.complicanzeForm.updateValueAndValidity()
      }) as Subscription;

    const dolore$ = this.complicanzeForm.get(dolore)?.valueChanges
      .subscribe(value => {
        if (value.idDizionario === 411 || (typeof value === 'boolean' && value)) {
          if (this.complicanzeForm.controls['doloreSelected']) this.complicanzeForm.get('dolore')?.enable();
          this.complicanzeForm.get('noteDolore')?.enable();
          this.doloreArray.controls.forEach((c, index) => {
            const selected = c.get('selected');
            if (selected instanceof FormControl) {
              selected.enable();
            } else {
              console.warn(`Unexpected control at doloreArray[${index}]`, selected);
            }
          });
        } else {
          if (this.complicanzeForm.controls['doloreSelected']) this.complicanzeForm.get('dolore')?.disable();
          this.complicanzeForm.get('noteDolore')?.disable();
          this.complicanzeForm.get('noteDolore')?.reset();
          this.createDoloreFormArray();
        }
        this.complicanzeForm.updateValueAndValidity()
      }) as Subscription;

    const infezioneContaminazioneMultiresistente$ = this.complicanzeForm.get(infezioneContaminazioneMultiresistente)?.valueChanges
      .subscribe(value => {
        if (value.idDizionario === 411 || (typeof value === 'boolean' && value)) {
          if (this.complicanzeForm.controls['infezioneContaminazioneMultiresistenteSelected']) this.complicanzeForm.get('infezioneContaminazioneMultiresistente')?.enable();
          this.complicanzeForm.get('noteMultiresistente')?.enable();
        } else {
          if (this.complicanzeForm.controls['infezioneContaminazioneMultiresistenteSelected']) {
            this.complicanzeForm.get('infezioneContaminazioneMultiresistente')?.disable();
            this.complicanzeForm.get('infezioneContaminazioneMultiresistente')?.reset();
          }
          this.complicanzeForm.get('noteMultiresistente')?.disable();
          this.complicanzeForm.get('noteMultiresistente')?.reset();
        }
        this.complicanzeForm.updateValueAndValidity()
      }) as Subscription;

    const sepsi$ = this.complicanzeForm.get(sepsi)?.valueChanges
      .subscribe(value => {
        if (value.idDizionario === 411 || (typeof value === 'boolean' && value)) {
          if (this.complicanzeForm.controls['sepsiSelected']) this.complicanzeForm.get('sepsi')?.enable();
          this.complicanzeForm.get('noteSepsi')?.enable();
        } else {
          if (this.complicanzeForm.controls['sepsiSelected']) {
            this.complicanzeForm.get('sepsi')?.disable();
            this.complicanzeForm.get('sepsi')?.reset();
          }
          this.complicanzeForm.get('noteSepsi')?.disable();
          this.complicanzeForm.get('noteSepsi')?.reset();
        }
        this.complicanzeForm.updateValueAndValidity()
      }) as Subscription;

    this.subscriptions = [
      osteomielite$,
      ossificazioneEterotopiche$,
      lesioniDaPressione$,
      spasticita$,
      trombosiVenosaProfonda$,
      emboliaPolmonare$,
      dolore$,
      infezioneContaminazioneMultiresistente$,
      sepsi$
    ];
  }

  handleComplicanzaChange(
    formArray: FormArray,
    index: number,
    checked: boolean,
    altroFieldName: string,
    altroEnumValue: number,
    nessunaEnumValue?: number
  ): void {
    const item = formArray.at(index) as FormGroup;
    const idDizionario = item.get('idDizionario')?.value;
    const idDizionario2 = item.get('idDizionario2');

    item.get('selected')?.setValue(checked);

    if (nessunaEnumValue && idDizionario === nessunaEnumValue && checked) {
      formArray.controls.forEach((control, i) => {
        if (i !== index) {
          control.get('selected')?.setValue(false);
          control.get('idDizionario2')?.reset();
          control.get('idDizionario2')?.clearValidators();
          control.get('idDizionario2')?.disable();

          if (control.get('idDizionario')?.value === altroEnumValue) {
            this.complicanzeForm.get(altroFieldName)?.reset();
            this.complicanzeForm.get(altroFieldName)?.disable();
          }
        }
      });

      idDizionario2?.reset();
      idDizionario2?.clearValidators();
      idDizionario2?.disable();
    }
    else if (checked) {
      if (nessunaEnumValue) {
        formArray.controls.forEach(control => {
          if (control.get('idDizionario')?.value === nessunaEnumValue) {
            control.get('selected')?.setValue(false);
            control.get('idDizionario2')?.reset();
            control.get('idDizionario2')?.clearValidators();
            control.get('idDizionario2')?.disable();
          }
        });
      }

      idDizionario2?.setValidators(Validators.required);
      idDizionario2?.enable();

      if (idDizionario === altroEnumValue) {
        this.complicanzeForm.get(altroFieldName)?.setValidators(Validators.required);
        this.complicanzeForm.get(altroFieldName)?.enable();
      }
    } else {
      idDizionario2?.reset();
      idDizionario2?.clearValidators();
      idDizionario2?.disable();

      if (idDizionario === altroEnumValue) {
        this.complicanzeForm.get(altroFieldName)?.reset();
        this.complicanzeForm.get(altroFieldName)?.disable();
      }
    }

    idDizionario2?.updateValueAndValidity();
  }

  // Gestisce il cambio delle complicanze respiratorie
  onComplicanzaRespiratoriaChange(index: number, checked: boolean): void {
    this.handleComplicanzaChange(
      this.complicanzeRespiratorieArray,
      index,
      checked,
      'altroComplicanzeRespiratorie',
      this.EComplicanzeAltroNessuna.ALTRO_COMPL_RESP,
      this.EComplicanzeAltroNessuna.NESSUNO_COMPL_RESP
    );
  }

  // Gestisce il cambio delle complicanze urologiche
  onComplicanzaUrologicaChange(index: number, checked: boolean): void {
    this.handleComplicanzaChange(
      this.complicanzeUrologicheArray,
      index,
      checked,
      'altroComplicanzeUrologiche',
      this.EComplicanzeAltroNessuna.ALTRO_URO,
      this.EComplicanzeAltroNessuna.NESSUNA_URO
    );
  }

  // Gestisce il cambio delle complicanze del rachide
  onComplicanzaRachideChange(index: number, checked: boolean): void {
    this.handleComplicanzaChange(
      this.rachideArray,
      index,
      checked,
      'altroRachide',
      this.EComplicanzeAltroNessuna.ALTRO_RACHIDE,
      this.EComplicanzeAltroNessuna.NESSUNA_RACHIDE
    );
  }

  // Apre il popup per le note
  openPopupNote(controlName: string): void {
    const noteControl = this.complicanzeForm.get(controlName)


    if (noteControl) {
      let configNote = this.modalService.createNoteParam(noteControl.value, this.readOnly)

      this.modalService.note(configNote)?.subscribe((res) => {
        if (res != noteControl.value) {
          noteControl.setValue(res)
          noteControl.markAsDirty()
          noteControl.updateValueAndValidity()
        }
      })
    }
  }


  // Crea un nuovo FormGroup per ogni sede di ossificazione
  createOssificazioneItem(sede: DizionarioModel): FormGroup {
    return this.fb.group({
      idDizionario1: [sede.idDizionario], // ID della sede
      idDizionario2: [{ value: null, disabled: true }],              // ID limitante (null inizialmente)
      selected: [{ value: false, disabled: true }]                   // Checkbox selezionato o no
    });
  }

  // Aggiungi un elemento al FormArray
  addOssificazioneItem(sede: DizionarioModel): void {
    this.ossificazioneArray.push(this.createOssificazioneItem(sede));
  }

  createOssificazFormArray() {
    this.ossificazioneArray.clear()
    this.optionsSedeOssificazione.forEach(sede => {
      this.addOssificazioneItem(sede);
    });

  }

  toggleArrayStatus(key: string, selectedOption: DizionarioModel): void {
    const array = this.getArrayByKey(key);
    if (!array) return;

    array.controls.forEach(group => {
      const fg = group as FormGroup;
      if (selectedOption?.idDizionario === 411) {
        fg.get('selected')?.enable();
        fg.get('idDizionario2')?.disable();
      } else {
        fg.get('selected')?.disable();
        fg.get('idDizionario2')?.reset();
        fg.get('idDizionario2')?.disable();
      }
    });
  }

  toggleRelativeRadioLimitante(index: number, checked: boolean, control?: AbstractControl): void {
    if (!control) return;

    const altroCtrl = this.complicanzeForm.get('altroSedeOssificazione');
    const idDizionario2Ctrl = this.ossificazioneArray.at(index).get('idDizionario2');
    const isAltroCheckbox = this.optionsSedeOssificazione[index].idDizionario === this.EComplicanzeAltroNessuna.ALTRO_OSS;

    if (checked) {
      if (isAltroCheckbox && altroCtrl) {
        altroCtrl.setValidators(Validators.required);
        altroCtrl.enable({ emitEvent: false });
      }
      if (idDizionario2Ctrl) {
        idDizionario2Ctrl.setValidators(Validators.required);
        idDizionario2Ctrl.enable({ emitEvent: false });
      }
    } else {
      if (isAltroCheckbox && altroCtrl) {
        altroCtrl.reset();
        altroCtrl.clearValidators();
        altroCtrl.disable({ emitEvent: false });
      }
      if (idDizionario2Ctrl) {
        idDizionario2Ctrl.reset();
        idDizionario2Ctrl.clearValidators();
        idDizionario2Ctrl.disable({ emitEvent: false });
      }
    }

    altroCtrl?.updateValueAndValidity({ emitEvent: false });
    idDizionario2Ctrl?.updateValueAndValidity({ emitEvent: false });
  }


  toggleLimitanteRadioSelection(
    option: any,
    event: MouseEvent,
    control: AbstractControl | null
  ): void {
    if (!control) return;

    const currentValue = control.get('idDizionario1')?.value;
    const selectedValue = option?.idDizionario;

    const isMatch = currentValue === selectedValue;

    if (isMatch) {
      event.preventDefault();
      setTimeout(() => {
        control.get('idDizionario1')?.setValue(null);
        control.get('idDizionario1')?.updateValueAndValidity({ emitEvent: true });
        control.get('idDizionario2')?.reset();

        const radioButton = event.target as HTMLElement;
        const input = radioButton.closest('mat-radio-button')?.querySelector('input[type="radio"]') as HTMLInputElement;
        input?.blur();
      }, 0);
    } else {
      control.get('idDizionario1')?.setValue(selectedValue);
      control.get('idDizionario2')?.setValidators(Validators.required);
    }
  }

  ngOnDestroy() {
    this.loadDecoders$?.unsubscribe()
    this.subscriptions?.forEach(sub => sub?.unsubscribe())
  }

  // Crea un nuovo FormGroup per ogni sede di lesione da pressione
  createLesionePressioneItem(sede: DizionarioModel): FormGroup {
    return this.createComplicanzaItem(sede, true, true);
  }

  // Aggiungi un elemento al FormArray delle lesioni da pressione
  addLesionePressioneItem(sede: DizionarioModel): void {
    this.lesioniPressioneArray.push(this.createLesionePressioneItem(sede));
  }

  // Gestisce la selezione/deselezione di una sede di lesione
  toggleLesionePressione(index: number, checked: boolean): void {
    const item = this.lesioniPressioneArray.at(index) as FormGroup;
    const idDizionario = item.get('idDizionario')?.value;

    if (!checked) {
      // Se deselezionato, resetta anche il valore dello stadio
      item.get('numero')?.setValue(null);
      item.get('numero')?.disable();

      // Se è l'opzione "Altro", resetta e disabilita il campo descrizione
      if (idDizionario === this.EComplicanzeAltroNessuna.ALTRO_LES_PRES) {
        this.complicanzeForm.get('altroSedePressione')?.reset();
        this.complicanzeForm.get('altroSedePressione')?.disable();
      }
    } else {
      item.get('numero')?.enable();
      item.get('numero')?.setValidators([Validators.required]);

      // Se è l'opzione "Altro", abilita il campo descrizione
      if (idDizionario === this.EComplicanzeAltroNessuna.ALTRO_LES_PRES) {
        this.complicanzeForm.get('altroSedePressione')?.setValidators(Validators.required);
        this.complicanzeForm.get('altroSedePressione')?.enable();
      }
    }

    // Aggiorna lo stato di selezione
    item.get('selected')?.setValue(checked);
    item.get('selected')?.updateValueAndValidity();
    item.get('numero')?.updateValueAndValidity();
  }

  // Crea il FormArray per le lesioni da pressione
  createLesioniPressioneFormArray() {
    this.lesioniPressioneArray.clear()
    this.optionsSedeLesionePressione.forEach(sede => {
      this.addLesionePressioneItem(sede);
    });
  }


  // Crea un nuovo FormGroup per ogni sede di trombosi
  createTrombosiItem(sede: DizionarioModel): FormGroup {
    return this.createComplicanzaItem(sede, false, true);
  }

  // Aggiungi un elemento al FormArray delle trombosi
  addTrombosiItem(sede: DizionarioModel): void {
    this.trombosiArray.push(this.createTrombosiItem(sede));
  }


  // Gestisce la selezione/deselezione di una sede di trombosi
  toggleTrombosi(index: number, checked: boolean): void {
    const item = this.trombosiArray.at(index) as FormGroup;
    item.get('selected')?.setValue(checked);
  }

  // Crea il FormArray per le trombosi
  createTrombosiFormArray() {
    this.trombosiArray.clear()
    this.optionsSedeTrombosi.forEach(sede => {
      this.addTrombosiItem(sede);
    });
  }

  createComplicanzaItem(item: DizionarioModel, hasNumericValue: boolean = false, disabled: boolean = false): FormGroup {
    const formGroup: any = {
      idDizionario: [item.idDizionario],
      selected: [{ value: false, disabled: disabled }]
    };

    if (hasNumericValue) {
      formGroup.numero = [{ value: null, disabled: true }];
    }

    return this.fb.group(formGroup);
  }

  // Crea un nuovo FormGroup per ogni complicanza respiratoria
  createComplicanzaRespiratoriaItem(complicanza: DizionarioModel): FormGroup {
    if (this.isDimissione) {
      return this.createComplicanzaItem(complicanza);
    } else {
      return this.fb.group({
        idDizionario: [complicanza.idDizionario],
        idDizionario2: [{ value: null, disabled: true }],
        selected: [false]
      });
    }
  }

  // Crea un nuovo FormGroup per ogni complicanza urologica
  createComplicanzaUrologicaItem(complicanza: DizionarioModel): FormGroup {
    if (this.isDimissione) {
      return this.createComplicanzaItem(complicanza);
    } else {
      return this.fb.group({
        idDizionario: [complicanza.idDizionario],
        idDizionario2: [{ value: null, disabled: true }],
        selected: [false]
      });
    }
  }

  // Aggiungi un elemento al FormArray delle complicanze respiratorie
  addComplicanzaRespiratoriaItem(complicanza: DizionarioModel): void {
    this.complicanzeRespiratorieArray.push(this.createComplicanzaRespiratoriaItem(complicanza));
  }

  // Crea il FormArray per le complicanze respiratorie
  createComplicanzeRespiratorieFormArray() {
    const altroIndex = this.optionsComplicanzeRespiratorie.findIndex(
      c => c.idDizionario === EComplicanzeAltroNessuna.ALTRO_COMPL_RESP
    );
    if (altroIndex !== -1) {
      const [altro] = this.optionsComplicanzeRespiratorie.splice(altroIndex, 1);
      this.optionsComplicanzeRespiratorie.push(altro);
    }
    this.complicanzeRespiratorieArray.clear()
    this.optionsComplicanzeRespiratorie.forEach(complicanza => {
      this.addComplicanzaRespiratoriaItem(complicanza);
    });
  }


  // Aggiungi un elemento al FormArray delle complicanze urologiche
  addComplicanzaUrologicaItem(complicanza: DizionarioModel): void {
    this.complicanzeUrologicheArray.push(this.createComplicanzaUrologicaItem(complicanza));
  }

  // Crea il FormArray per le complicanze urologiche
  createComplicanzeUrologicheFormArray() {
    const altroIndex = this.optionsComplicanzeUrologiche.findIndex(
      c => c.idDizionario === EComplicanzeAltroNessuna.ALTRO_URO
    );
    if (altroIndex !== -1) {
      const [altro] = this.optionsComplicanzeUrologiche.splice(altroIndex, 1);
      this.optionsComplicanzeUrologiche.push(altro);
    }
    this.complicanzeUrologicheArray.clear()
    this.optionsComplicanzeUrologiche.forEach(complicanza => {
      this.addComplicanzaUrologicaItem(complicanza);
    });
  }

  createRachideItem(complicanza: DizionarioModel): FormGroup {
    if (this.isDimissione) {
      return this.createComplicanzaItem(complicanza);
    } else {
      return this.fb.group({
        idDizionario: [complicanza.idDizionario],
        idDizionario2: [{ value: null, disabled: true }],
        selected: [false]
      });
    }
  }

  // Aggiungi un elemento al FormArray del rachide
  addRachideItem(complicanza: DizionarioModel): void {
    this.rachideArray.push(this.createRachideItem(complicanza));
  }

  // Crea il FormArray per le complicanze del rachide
  createRachideFormArray(): void {
    this.rachideArray.clear();
    this.optionsRachide.forEach(complicanza => {
      this.addRachideItem(complicanza);
    });
  }

  createDoloreItem(dolore: DizionarioModel): FormGroup {
    return this.isDimissione ?
      this.fb.group({
        idDizionario: [dolore.idDizionario], // ID della complicanza
        selected: [{ value: false, disabled: true }]  // Checkbox selezionato o no
      })
      : this.fb.group({
        idDizionario1: [dolore.idDizionario], // ID della checkbox
        idDizionario2: [{ value: null, disabled: true }],              // ID limitante (null inizialmente)
        selected: [{ value: false, disabled: true }]                   // Checkbox selezionato o no
      });
  }

  addDoloreItem(dolore: DizionarioModel): void {
    this.doloreArray.push(this.createDoloreItem(dolore));
  }

  createDoloreFormArray() {
    this.doloreArray.clear()
    this.optionsDoloreComplicanze.forEach(dolore => {
      this.addDoloreItem(dolore);
    });
  }

  toggleDolore(index: number, checked: boolean): void {
    const item = this.doloreArray.at(index) as FormGroup;
    item.get('selected')?.setValue(checked);

    const radioControl = item.get('idDizionario2');

    if (checked) {
      radioControl?.setValidators(Validators.required);
      radioControl?.enable();
    } else {
      radioControl?.clearValidators();
      radioControl?.reset();
      radioControl?.disable();
    }
    radioControl?.updateValueAndValidity();
  }

  toggleStadioLesionePressione(index: number, numero: number): void {
    const item = this.lesioniPressioneArray.at(index) as FormGroup;
    toggleRadioSelection(item, 'numero', numero);
  }

  getSelectedItems(formArray: FormArray): { idDizionario: number }[] {
    return formArray.value.filter((item: any) => item.selected);
  }

  formatIntercorse(label: string, singolare: 'm' | 'f'): string {
    if (label !== 'INTERCORSE') return label.replace(/Presenti\b/i, 'Presente');
    return singolare === 'f'
      ? label.replace(/Intercorse\b/i, 'Intercorsa')
      : label.replace(/Intercorse\b/i, 'Intercorso');
  }

  get complicanzeRespiratorieArray(): FormArray {
    return this.formGroup.get(
      this.isDimissione ? 'complicanzeRespiratorieDimissioneLista' : 'complicanzeRespiratorie'
    ) as FormArray;
  }

  get complicanzeUrologicheArray(): FormArray {
    return this.formGroup.get(
      this.isDimissione ? 'complicanzeUrologicheDimissioneLista' : 'complicanzeUrologiche'
    ) as FormArray;
  }

  get rachideArray(): FormArray {
    return this.formGroup.get(
      this.isDimissione ? 'rachideDimissioneLista' : 'rachideLista'
    ) as FormArray;
  }

  get trombosiArray(): FormArray {
    return this.formGroup.get(
      this.isDimissione ? 'trombosiDimissioneLista' : 'trombosiLista'
    ) as FormArray;
  }

  get doloreArray(): FormArray {
    return this.formGroup.get(
      this.isDimissione ? 'doloreDimissioneLista' : 'doloreLista'
    ) as FormArray;
  }

  get lesioniPressioneArray(): FormArray {
    return this.formGroup.get('lesioniPressioneLista') as FormArray;
  }

  get ossificazioneArray(): FormArray {
    const key = this.isDimissione
      ? 'ossificazioneDimissioneLista'
      : 'ossificazioneLista';
    return this.complicanzeForm.get(key) as FormArray;
  }

  getArrayByKey(key: string): FormArray | null {
    switch (key) {
      case 'complicanzeRespiratorie':
        return this.complicanzeRespiratorieArray;
      case 'complicanzeUrologiche':
        return this.complicanzeUrologicheArray;
      case 'rachide':
        return this.rachideArray;
      case 'trombosiVenosaProfonda':
      case 'trombosi':
        return this.trombosiArray;
      case 'dolore':
        return this.doloreArray;
      case 'lesioniPressione':
        return this.lesioniPressioneArray;
      case 'ossificazioneEterotopiche':
        return this.ossificazioneArray;
      default:
        return null;
    }
  }

  get doloreControl(): FormControl {
    return this.complicanzeForm.get(this.isDimissione ? 'dolore' : 'doloreSelected') as FormControl;
  }

}
