<form [formGroup]="complicanzeForm" class="box-dati mt-4">
    <div>
        <!-- Ossificazioni Eterotopiche -->
        <div class="row mb-2  align-items-center">
            <div class="d-flex pl-0 align-items-center mb-2 col-12">
                <div class="pl-0 col-3">
                    <label class="field-label">
                        Ossificazioni Eterotopiche<span *ngIf="ossificazioneEterotopicheRequired">*</span>
                    </label>
                </div>
                <div class="pl-0 col-12">
                    @if (isDimissione) {
                    <mat-radio-group formControlName="ossificazioneEterotopiche" class="d-flex">
                        <mat-radio-button
                            (click)="toggleRadioSelection(complicanzeForm, 'ossificazioneEterotopiche',true)"
                            [value]="true" color="primary">Si
                        </mat-radio-button>
                        <mat-radio-button
                            (click)="toggleRadioSelection(complicanzeForm, 'ossificazioneEterotopiche',false)"
                            [value]="false" class="ms-4" color="primary">No
                        </mat-radio-button>
                    </mat-radio-group>
                    } @else if (optionsPresentiIntercorseSiNo?.siNo?.length) {
                        <mat-radio-group formControlName="ossificazioneEterotopicheSelected">
                            <!--                            SI-->
                            <mat-radio-button
                                    [checked]="(complicanzeForm.get('ossificazioneEterotopiche')?.value?.idDizionario && complicanzeForm.get('ossificazioneEterotopiche')?.value?.idDizionario !== 248)
                                                || complicanzeForm.get('ossificazioneEterotopicheSelected')?.value?.idDizionario === 411"
                                    (click)="toggleRadioSelection(complicanzeForm, 'ossificazioneEterotopicheSelected',optionsPresentiIntercorseSiNo.siNo[0])"
                                    [value]="optionsPresentiIntercorseSiNo.siNo[0]"
                                    color="primary">
                                {{ optionsPresentiIntercorseSiNo.siNo[0].descrizione | capitalizeFirst }}
                            </mat-radio-button>
                            <!--                        NO-->
                            <mat-radio-button
                                    [checked]="complicanzeForm.get('ossificazioneEterotopiche')?.value?.idDizionario === 248"
                                    (click)="toggleRadioSelection(complicanzeForm, 'ossificazioneEterotopicheSelected',optionsPresentiIntercorseSiNo.siNo[1])"
                                    [value]="optionsPresentiIntercorseSiNo.siNo[1]"
                                    color="primary">
                                {{ optionsPresentiIntercorseSiNo.siNo[1].descrizione | capitalizeFirst }}
                            </mat-radio-button>
                        </mat-radio-group>
                        <!--                        presenti intercorse     -->
                        <mat-radio-group formControlName="ossificazioneEterotopiche">
                            <mat-radio-button *ngFor="let option of optionsPresentiIntercorseSiNo?.presenti"
                                              [checked]="complicanzeForm.get('ossificazioneEterotopiche')?.value?.idDizionario === option.idDizionario"
                                              [disabled]="complicanzeForm.get('ossificazioneEterotopiche')?.value?.idDizionario === 248"
                                              (click)="toggleRadioSelection(complicanzeForm, 'ossificazioneEterotopiche', option)"
                                              [value]="option" color="primary">
                                {{ option.descrizione | capitalizeFirst }}
                            </mat-radio-button>
                        </mat-radio-group>
                    }
                </div>
            </div>
        </div>

        <!-- Sede Ossificazioni -->
        <div class="row mb-3 border-bottom pb-3">
            <div class="col-12">
                <div>
                    <div class="pl-0 col-6 row align-items-center">
                        <label class="field-label col-3 pl-0 mb-3">Sede<span
                                *ngIf="complicanzeForm.get(isDimissione ? 'ossificazioneEterotopiche' : 'ossificazioneEterotopicheSelected')?.value">*</span></label>
                        <div class="col-4 d-flex align-items-center">
                            <button mat-button type="button" class="p-0 note-button"
                                (mousedown)="openPopupNote('noteOssificazione')">
                                <svg class="icon icon-primary">
                                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                </svg>
                                <span class="ms-1">Note</span>
                            </button>
                        </div>
                    </div>
                    <div class="row"
                        [formArrayName]="isDimissione ? 'ossificazioneDimissioneLista' : 'ossificazioneLista'">
                        <div class="col-5 mb-2 pl-0" *ngFor="let control of ossificazioneArray.controls; let i = index">
                            <div class="d-flex align-items-center" [formGroupName]="i">
                                <mat-checkbox (change)="toggleRelativeRadioLimitante(i, $event.checked, control)"
                                    class="pl-0 col-4" formControlName="selected" color="primary">
                                    {{ optionsSedeOssificazione[i].descrizione | capitalizeFirst }}
                                </mat-checkbox>
                                <!--altro-->
                                <div class="col-8 mb-2 pl-0" [formGroup]="complicanzeForm"
                                    *ngIf="optionsSedeOssificazione[i].idDizionario === EComplicanzeAltroNessuna.ALTRO_OSS">
                                    <mat-form-field appearance="outline" class="pl-0 col-12">
                                        <input matInput formControlName="altroSedeOssificazione"
                                            [matTooltip]="complicanzeForm.get('altroSedeOssificazione')?.value"
                                            [placeholder]="ossificazioneArray.controls[i].get('selected')?.value ? 'Inserisci*' : 'Inserisci'"
                                            maxlength="250" />
                                        <mat-error
                                            *ngIf="complicanzeForm.get('altroSedeOssificazione')?.hasError('required')">
                                            {{ ERROR_MESSAGE.REQUIRED }}
                                        </mat-error>
                                    </mat-form-field>
                                </div>
                                <!--radio limitante-->
                                <mat-radio-group class="pl-0 col-6" formControlName="idDizionario2">
                                    <mat-radio-button *ngFor="let limitante of optionsLimitante"
                                        (click)="toggleLimitanteRadioSelection(limitante, $event, ossificazioneArray.controls[i])"
                                        [value]="limitante" color="primary">
                                        {{ limitante.descrizione | capitalizeFirst }}
                                    </mat-radio-button>
                                </mat-radio-group>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Lesioni da Pressione -->
        <div class="row mb-2 align-items-center">
            <div class="pl-0 d-flex align-items-center col-6 col-md-6 mb-2">
                <div class="pl-0 col-4">
                    <label class="field-label">
                        Lesioni da Pressione<span *ngIf="lesioniPressioneRequired">*</span>
                    </label>
                </div>
                <div class="pl-0 col-5">
                    @if (isDimissione) {
                    <mat-radio-group formControlName="lesioniPressione" class="d-flex">
                        <mat-radio-button (click)="toggleRadioSelection(complicanzeForm, 'lesioniPressione',true)"
                            [value]="true" color="primary">Si
                        </mat-radio-button>
                        <mat-radio-button (click)="toggleRadioSelection(complicanzeForm, 'lesioniPressione',false)"
                            [value]="false" class="ms-4" color="primary">No
                        </mat-radio-button>
                    </mat-radio-group>
                    } @else if (optionsPresentiIntercorseSiNo?.siNo?.length) {
                        <mat-radio-group formControlName="lesioniPressioneSelected">
                            <!--                            SI-->
                            <mat-radio-button
                                    [checked]="(complicanzeForm.get('lesioniPressione')?.value?.idDizionario && complicanzeForm.get('lesioniPressione')?.value?.idDizionario !== 248)
                                                || complicanzeForm.get('lesioniPressioneSelected')?.value?.idDizionario === 411"
                                    (click)="toggleRadioSelection(complicanzeForm, 'lesioniPressioneSelected',optionsPresentiIntercorseSiNo.siNo[0])"
                                    [value]="optionsPresentiIntercorseSiNo.siNo[0]"
                                    color="primary">
                                {{ optionsPresentiIntercorseSiNo.siNo[0].descrizione | capitalizeFirst }}
                            </mat-radio-button>
                            <!--                        NO-->
                            <mat-radio-button
                                    [checked]="complicanzeForm.get('lesioniPressione')?.value?.idDizionario === 248"
                                    (click)="toggleRadioSelection(complicanzeForm, 'lesioniPressioneSelected',optionsPresentiIntercorseSiNo.siNo[1])"
                                    [value]="optionsPresentiIntercorseSiNo.siNo[1]"
                                    color="primary">
                                {{ optionsPresentiIntercorseSiNo.siNo[1].descrizione | capitalizeFirst }}
                            </mat-radio-button>
                        </mat-radio-group>
                        <!--                        presenti intercorse     -->
                        <mat-radio-group formControlName="lesioniPressione">
                            <mat-radio-button *ngFor="let option of optionsPresentiIntercorseSiNo?.presenti"
                                              [checked]="complicanzeForm.get('lesioniPressione')?.value?.idDizionario === option.idDizionario"
                                              [disabled]="complicanzeForm.get('lesioniPressione')?.value?.idDizionario === 248"
                                              (click)="toggleRadioSelection(complicanzeForm, 'lesioniPressione', option)"
                                              [value]="option" color="primary">
                                {{ option.descrizione | capitalizeFirst }}
                            </mat-radio-button>
                        </mat-radio-group>
                    }
                </div>
                <div class="col-5" *ngIf="complicanzeForm.controls['lesioniPressioneSelected']">
                    <div>
                        <div>
                            <mat-radio-group formControlName="lesioniPressione" class="d-flex">
                                <mat-radio-button *ngFor="let option of optionsPresentiIntercorseSiNo?.presenti"
                                    (click)="datiCliniciService.toggleRadioSelection(complicanzeForm.get('lesioniPressione'), option, $event)"
                                    [value]="option" color="primary">
                                    {{ option.descrizione | capitalizeFirst }}
                                </mat-radio-button>
                            </mat-radio-group>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sede Lesioni da Pressione -->
        <div class="row mb-3 border-bottom pb-3">
            <div class="col-12">
                <div>
                    <div class="pl-0 col-6 row align-items-center">
                        <label class="field-label col-3 pl-0 mb-3">Sede<span
                                *ngIf="complicanzeForm.get(isDimissione ? 'lesioniPressione' : 'lesioniPressioneSelected')?.value">*</span></label>
                        <div class="col-4 d-flex align-items-center">
                            <button mat-button type="button" class="p-0 note-button"
                                (mousedown)="openPopupNote('notePressione')">
                                <svg class="icon icon-primary">
                                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                </svg>
                                <span class="ms-1">Note</span>
                            </button>
                        </div>
                    </div>
                    <div class="row" formArrayName="lesioniPressioneLista">
                        <div class="col-5 mb-2 pl-0"
                            *ngFor="let controlLesioni of lesioniPressioneArray.controls; let i = index">
                            <div class="d-flex align-items-center" [formGroupName]="i">
                                <mat-checkbox (change)="toggleLesionePressione(i, $event.checked)" class="col-4 pl-0"
                                    formControlName="selected" color="primary">
                                    {{ optionsSedeLesionePressione[i].descrizione | capitalizeFirst }}
                                </mat-checkbox>
                                <!--                          ALTRO-->
                                <div class="col-8 mb-2 pl-0" [formGroup]="complicanzeForm"
                                    *ngIf="controlLesioni.get('idDizionario')?.value === EComplicanzeAltroNessuna.ALTRO_LES_PRES">
                                    <mat-form-field class="col-12 p-0" appearance="outline">
                                        <input matInput formControlName="altroSedePressione"
                                            [matTooltip]="complicanzeForm.get('altroSedePressione')?.value"
                                            [placeholder]="lesioniPressioneArray.controls[i].get('selected')?.value ? 'Inserisci*' : 'Inserisci'"
                                            maxlength="250" />
                                        <mat-error
                                            *ngIf="complicanzeForm.get('altroSedePressione')?.hasError('required')">
                                            {{ ERROR_MESSAGE.REQUIRED }}
                                        </mat-error>
                                    </mat-form-field>
                                </div>
                                <!--                                radio 1 2 3 4-->
                                <mat-radio-group class="col-5 d-flex justify-content-between align-items-center"
                                    formControlName="numero">
                                    <label class="field-label">Stadio</label>
                                    <mat-radio-button (click)="toggleStadioLesionePressione(i, stadio)"
                                        *ngFor="let stadio of optionsStadio" [value]="stadio" color="primary">
                                        {{ stadio }}
                                    </mat-radio-button>
                                </mat-radio-group>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--    &lt;!&ndash; Osteomielite &ndash;&gt;-->
        <div class="row mb-3 border-bottom pb-3">
            <div class="col-12 d-flex align-items-center pe-md-2 mb-3 mb-md-0">
                <div class="col-2">
                    <label class="align-self-center field-label">
                        Osteomielite<span *ngIf="osteomieliteRequired">*</span>
                    </label>
                </div>
                <div class="col-8 d-flex">
                    @if (isDimissione) {
                    <mat-radio-group formControlName="osteomielite" class="d-flex">
                        <mat-radio-button (click)="toggleRadioSelection(complicanzeForm, 'osteomielite',true)"
                            [value]="true" color="primary">Si
                        </mat-radio-button>
                        <mat-radio-button (click)="toggleRadioSelection(complicanzeForm, 'osteomielite',false)"
                            [value]="false" class="ms-4" color="primary">No
                        </mat-radio-button>
                    </mat-radio-group>
                    } @else {
                    <mat-radio-group formControlName="osteomieliteSelected" class="d-flex">
                        <mat-radio-button *ngFor="let option of optionsPresentiIntercorseSiNo?.siNo"
                            (click)="toggleRadioSelection(complicanzeForm, 'osteomieliteSelected', $event)"
                            [value]="option" color="primary">
                            {{ option.descrizione | capitalizeFirst }}
                        </mat-radio-button>
                    </mat-radio-group>
                    }
                    <div class="col-4 d-flex align-items-center"
                        *ngIf="complicanzeForm.controls['osteomieliteSelected']">
                        <div class="ml-2 ">
                            <mat-radio-group [required]="complicanzeForm.get('osteomielite')?.value === true"
                                formControlName="osteomielite" class="d-flex">
                                <mat-radio-button *ngFor="let option of optionsPresentiIntercorseSiNo?.presenti"
                                    (click)="datiCliniciService.toggleRadioSelection(complicanzeForm.get('osteomielite'), option, $event)"
                                    [value]="option" color="primary">
                                    {{ formatIntercorse(option.descrizione, 'f') | capitalizeFirst }}
                                </mat-radio-button>
                            </mat-radio-group>
                        </div>
                        <button mat-button type="button" class="ml-5 p-0 note-button"
                            (click)="openPopupNote('noteOsteomielite')">
                            <svg class="icon mr-2 mb-1 icon-primary">
                                <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                            </svg>
                            <span class="pl-1 icon-primary">Note</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Trombosi venosa profonda -->
        <div class="row pb-3 ">
            <div class="col-12 d-flex align-items-center pe-md-2 mb-md-0">
                <div class="col-2">
                    <label class="align-self-center field-label">
                        Trombosi venosa profonda<span *ngIf="trombosiVenosaProfondaRequired">*</span>
                    </label>
                </div>
                <div class="col-8 d-flex">
                    @if (isDimissione) {
                    <mat-radio-group formControlName="trombosiVenosaProfonda" class="d-flex">
                        <mat-radio-button (click)="toggleRadioSelection(complicanzeForm, 'trombosiVenosaProfonda',true)"
                            [value]="true" color="primary">Si
                        </mat-radio-button>
                        <mat-radio-button
                            (click)="toggleRadioSelection(complicanzeForm, 'trombosiVenosaProfonda',false)"
                            [value]="false" class="ms-4" color="primary">No
                        </mat-radio-button>
                    </mat-radio-group>
                    } @else {
                    <mat-radio-group formControlName="trombosiVenosaProfondaSelected" class="d-flex">
                        <mat-radio-button *ngFor="let option of optionsPresentiIntercorseSiNo?.siNo"
                            (click)="toggleRadioSelection(complicanzeForm, 'trombosiVenosaProfondaSelected', $event)"
                            [value]="option" color="primary">
                            {{ option.descrizione | capitalizeFirst }}
                        </mat-radio-button>
                    </mat-radio-group>
                    }
                    <div class="ml-5 d-flex ps-md-2" *ngIf="complicanzeForm.controls['trombosiVenosaProfondaSelected']">
                        <div>
                            <mat-radio-group
                                [required]="complicanzeForm.get('trombosiVenosaProfondaSelected')?.value === true"
                                formControlName="trombosiVenosaProfonda" class="d-flex">
                                <mat-radio-button *ngFor="let option of optionsPresentiIntercorseSiNo?.presenti"
                                    (click)="datiCliniciService.toggleRadioSelection(complicanzeForm.get('trombosiVenosaProfondaSelected'), option, $event)"
                                    [value]="option" color="primary">
                                    {{ formatIntercorse(option.descrizione, 'f') | capitalizeFirst }}
                                </mat-radio-button>
                            </mat-radio-group>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sede Trombosi -->
        <div class="row mb-3 border-bottom pb-3">
            <div class="col-12">
                <div>
                    <div class="col-6 row align-items-center">
                        <label class="field-label col-5 mb-3">Sede<span
                                *ngIf="complicanzeForm.get(isDimissione ? 'trombosiVenosaProfonda' : 'trombosiVenosaProfondaSelected')?.value">*</span></label>
                    </div>
                    <!--                    checkbox-->
                    <div class="row" [formArrayName]="isDimissione ? 'trombosiDimissioneLista' : 'trombosiLista'">
                        <div class="pl-0 col-2 mb-2" *ngFor="let control of trombosiArray.controls; let i = index">
                            <div class="d-flex align-items-center" [formGroupName]="i">
                                <mat-checkbox (change)="toggleTrombosi(i, $event.checked)" class="pl-0"
                                    formControlName="selected" color="primary">
                                    {{ optionsSedeTrombosi[i].descrizione | capitalizeFirst }}
                                </mat-checkbox>
                            </div>
                        </div>
                        <div class="col-2 d-flex align-items-center">
                            <button mat-button type="button" class="p-0 note-button"
                                (mousedown)="openPopupNote('noteTrombosi')">
                                <svg class="icon icon-primary">
                                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                </svg>
                                <span class="ms-1">Note</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <!--     Embolia Polmonare &ndash;&gt;-->
        <div class="row mb-3 border-bottom pb-3">
            <div class="col-12 d-flex align-items-center pe-md-2 mb-3 mb-md-0">
                <div class="col-2">
                    <label class="align-self-center field-label">
                        Embolia Polmonare<span *ngIf="emboliaPolmonareRequired">*</span>
                    </label>
                </div>
                <div class="col-8 d-flex">
                    @if (isDimissione) {
                    <mat-radio-group formControlName="emboliaPolmonare" class="d-flex">
                        <mat-radio-button (click)="toggleRadioSelection(complicanzeForm, 'emboliaPolmonare',true)"
                            [value]="true" color="primary">Si
                        </mat-radio-button>
                        <mat-radio-button (click)="toggleRadioSelection(complicanzeForm, 'emboliaPolmonare',false)"
                            [value]="false" class="ms-4" color="primary">No
                        </mat-radio-button>
                    </mat-radio-group>
                    } @else {
                    <mat-radio-group formControlName="emboliaPolmonareSelected" class="d-flex">
                        <mat-radio-button *ngFor="let option of optionsPresentiIntercorseSiNo?.siNo"
                            (click)="toggleRadioSelection(complicanzeForm, 'emboliaPolmonareSelected', $event)"
                            [value]="option" color="primary">
                            {{ option.descrizione | capitalizeFirst }}
                        </mat-radio-button>
                    </mat-radio-group>
                    }
                    <div class="ml-5 ps-md-2 d-flex">
                        <div class="ml-2 ">
                            <mat-radio-group *ngIf="complicanzeForm.controls['emboliaPolmonareSelected']"
                                [required]="complicanzeForm.get('emboliaPolmonareSelected')?.value === true"
                                formControlName="emboliaPolmonare" class="d-flex">
                                <mat-radio-button *ngFor="let option of optionsPresentiIntercorseSiNo?.presenti"
                                    (click)="datiCliniciService.toggleRadioSelection(complicanzeForm.get('emboliaPolmonareSelected'), option, $event)"
                                    [value]="option" color="primary">
                                    {{ formatIntercorse(option.descrizione, 'f') | capitalizeFirst }}
                                </mat-radio-button>
                            </mat-radio-group>
                        </div>
                        <button mat-button type="button" class="note-button p-0" (click)="openPopupNote('noteEmbolia')">
                            <svg class="icon mr-2 mb-1 icon-primary">
                                <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                            </svg>
                            <span class="pl-1 icon-primary">Note</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Complicanze Respiratorie -->
        <div class="row mb-3 border-bottom pb-3">
            <div class="col-12">
                <div>
                    <div class="col-12 mb-3 row align-items-center">
                        <label class="field-label col-3 pl-0">
                            Complicanze Respiratorie<span *ngIf="complicanzeRespiratorieRequired">*</span>
                        </label>
                        <div class="col-4 d-flex align-items-center">
                            <button mat-button type="button" class="p-0 note-button"
                                (mousedown)="openPopupNote('noteComplicanzeRespiratorie')">
                                <svg class="icon icon-primary">
                                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                </svg>
                                <span class="ms-1">Note</span>
                            </button>
                        </div>
                    </div>
                    <div class="row"
                        [formArrayName]="isDimissione ? 'complicanzeRespiratorieDimissioneLista' : 'complicanzeRespiratorie'">
                        <div class="col-5 mb-2 pl-0"
                            *ngFor="let control of complicanzeRespiratorieArray.controls; let i = index">
                            <div class="d-flex align-items-center" [formGroupName]="i">

                                <!-- Checkbox -->
                                <mat-checkbox (change)="onComplicanzaRespiratoriaChange(i, $event.checked)"
                                    class="pl-0 col-6" formControlName="selected" color="primary">
                                    {{ optionsComplicanzeRespiratorie[i].descrizione | capitalizeFirst }}
                                </mat-checkbox>

                                <!-- Altro input -->
                                <div class="col-8 mb-2 pl-0" [formGroup]="complicanzeForm"
                                    *ngIf="control.get('idDizionario')?.value === EComplicanzeAltroNessuna.ALTRO_COMPL_RESP">
                                    <mat-form-field appearance="outline" class="pl-0 col-12">
                                        <input matInput formControlName="altroComplicanzeRespiratorie"
                                            [matTooltip]="complicanzeForm.get('altroComplicanzeRespiratorie')?.value"
                                            [placeholder]="complicanzeRespiratorieArray.controls[i].get('selected')?.value ? 'Inserisci*' : 'Inserisci'"
                                            maxlength="250" />
                                        <mat-error
                                            *ngIf="complicanzeForm.get('altroComplicanzeRespiratorie')?.hasError('required')">
                                            {{ ERROR_MESSAGE.REQUIRED }}
                                        </mat-error>
                                    </mat-form-field>
                                </div>

                                <!-- Radio buttons -->
                                <mat-radio-group
                                    *ngIf="complicanzeForm.controls['complicanzeRespiratorie'] && optionsComplicanzeRespiratorie[i]?.descrizione !== 'NESSUNA'"
                                    class="pl-0 col-6" formControlName="idDizionario2">
                                    <mat-radio-button *ngFor="let option of optionsPresentiIntercorse"
                                        (click)="datiCliniciService.toggleRadioSelection(control, option, $event)"
                                        [value]="option" color="primary">
                                        {{ option.descrizione | capitalizeFirst }}
                                    </mat-radio-button>
                                </mat-radio-group>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Complicanze Urologiche -->
        <div class="row mb-3 border-bottom pb-3">
            <div class="col-12">
                <div>
                    <div class="col-12 mb-3 row align-items-center">
                        <label class="field-label col-3 pl-0">
                            Complicanze Urologiche<span *ngIf="complicanzeUrologicheRequired">*</span>
                        </label>
                        <div class="col-4 d-flex align-items-center">
                            <button mat-button type="button" class="p-0 note-button"
                                (mousedown)="openPopupNote('noteComplicanzeUrologiche')">
                                <svg class="icon icon-primary">
                                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                </svg>
                                <span class="ms-1">Note</span>
                            </button>
                        </div>
                    </div>
                    <div class="row"
                        [formArrayName]="isDimissione ? 'complicanzeUrologicheDimissioneLista' : 'complicanzeUrologiche'">
                        <div class="col-5 mb-2 pl-0"
                            *ngFor="let control of complicanzeUrologicheArray.controls; let i = index">
                            <div class="d-flex align-items-center" [formGroupName]="i">

                                <!-- Checkbox -->
                                <mat-checkbox (change)="onComplicanzaUrologicaChange(i, $event.checked)" class="pl-0"
                                    [ngClass]="control.get('idDizionario')?.value === EComplicanzeAltroNessuna.ALTRO_URO ? 'col-2' : 'col-6'"
                                    formControlName="selected" color="primary">
                                    {{ optionsComplicanzeUrologiche[i].descrizione | capitalizeFirst }}
                                </mat-checkbox>

                                <!-- Altro input -->
                                <div class="col-8 mb-2 pl-0" [formGroup]="complicanzeForm"
                                    *ngIf="control.get('idDizionario')?.value === EComplicanzeAltroNessuna.ALTRO_URO">
                                    <mat-form-field appearance="outline" class="pl-0 col-12">
                                        <input matInput formControlName="altroComplicanzeUrologiche"
                                            [matTooltip]="complicanzeForm.get('altroComplicanzeUrologiche')?.value"
                                            [placeholder]="complicanzeUrologicheArray.controls[i].get('selected')?.value ? 'Inserisci*' : 'Inserisci'"
                                            maxlength="250" />
                                        <mat-error
                                            *ngIf="complicanzeForm.get('altroComplicanzeUrologiche')?.hasError('required')">
                                            {{ ERROR_MESSAGE.REQUIRED }}
                                        </mat-error>
                                    </mat-form-field>
                                </div>

                                <!-- Radio buttons -->
                                <mat-radio-group
                                    *ngIf="complicanzeForm.controls['complicanzeUrologiche'] && optionsComplicanzeRespiratorie[i]?.descrizione !== 'NESSUNA'"
                                    class="pl-0 col-6" formControlName="idDizionario2">
                                    <mat-radio-button *ngFor="let option of optionsPresentiIntercorse"
                                        (click)="datiCliniciService.toggleRadioSelection(control, option, $event)"
                                        [value]="option" color="primary">
                                        {{ option.descrizione | capitalizeFirst }}
                                    </mat-radio-button>
                                </mat-radio-group>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rachide -->
        <div class="row mb-3 border-bottom pb-3">
            <div class="col-12">
                <div>
                    <div class="col-12 mb-3 row align-items-center">
                        <label class="field-label col-3 pl-0">
                            Rachide<span *ngIf="rachideRequired">*</span>
                        </label>
                        <div class="col-4 d-flex align-items-center">
                            <button mat-button type="button" class="p-0 note-button"
                                (mousedown)="openPopupNote('noteRachide')">
                                <svg class="icon icon-primary">
                                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                </svg>
                                <span class="ms-1">Note</span>
                            </button>
                        </div>
                    </div>
                    <div class="row" [formArrayName]="isDimissione ? 'rachideDimissioneLista' : 'rachideLista'">
                        <div [ngClass]="control.get('idDizionario')?.value === EComplicanzeAltroNessuna.ALTRO_RACHIDE ? 'col-12 pl-0' : 'col-6 mb-2 pl-0'"
                            *ngFor="let control of rachideArray.controls; let i = index" [formGroupName]="i">
                            <div class="d-flex align-items-center">
                                <mat-checkbox (change)="onComplicanzaRachideChange(i, $event.checked)" class="pl-0"
                                    [ngClass]="isDimissione ? 'col-4' : 'col-2'" formControlName="selected"
                                    color="primary">
                                    {{ optionsRachide[i].descrizione | capitalizeFirst }}
                                </mat-checkbox>
                                <div class="col-4 ms-4" [formGroup]="complicanzeForm"
                                    *ngIf="control.get('idDizionario')?.value === EComplicanzeAltroNessuna.ALTRO_RACHIDE">
                                    <mat-form-field class="col-12 p-0" appearance="outline">
                                        <input matInput formControlName="altroRachide"
                                            [matTooltip]="complicanzeForm.get('altroRachide')?.value"
                                            [placeholder]="rachideArray.controls[i].get('selected')?.value ? 'Inserisci*' : 'Inserisci'"
                                            maxlength="250" />
                                        <mat-error *ngIf="complicanzeForm.get('altroRachide')?.hasError('required')">
                                            {{ ERROR_MESSAGE.REQUIRED }}
                                        </mat-error>
                                    </mat-form-field>
                                </div>
                                <mat-radio-group *ngIf="complicanzeForm.controls['rachideLista']" class="col-4 d-flex"
                                    formControlName="idDizionario2">
                                    <mat-radio-button *ngFor="let option of optionsPresentiIntercorse"
                                        (click)="datiCliniciService.toggleRadioSelection(control, option, $event)"
                                        [value]="option" color="primary">
                                        {{ formatIntercorse(option.descrizione, 'f') | capitalizeFirst }}
                                    </mat-radio-button>
                                </mat-radio-group>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Spasticità – Scala Ashworth -->
        <div *ngIf="!isDimissione && complicanzeForm.controls['spasticitaSelected']">
            <div class="row pb-3">
                <div class="col-12 d-flex align-items-center pe-md-2 mb-md-0">
                    <div class="col-3">
                        <label class="field-label">
                            Spasticità – Scala Ashworth
                            <span *ngIf="spasticitaRequired">*</span>
                        </label>
                    </div>

                    <div class="col-8 d-flex">
                        <mat-radio-group [formControlName]="isDimissione ? 'spasticita' : 'spasticitaSelected'"
                            class="d-flex">
                            <mat-radio-button *ngFor="let option of optionsPresentiIntercorseSiNo?.presenti"
                                (click)="toggleRadioSelection(complicanzeForm, isDimissione ? 'spasticita' : 'spasticitaSelected', $event)"
                                [value]="option" color="primary">
                                {{ option.descrizione | capitalizeFirst }}
                            </mat-radio-button>
                        </mat-radio-group>
                        <div class="ms-5 ps-md-2">
                            <mat-radio-group formControlName="spasticita"
                                [required]="complicanzeForm.get('spasticitaSelected')?.value === true" class="d-flex">
                                <mat-radio-button *ngFor="let option of optionsPresentiIntercorseSiNo?.presenti"
                                    (click)="datiCliniciService.toggleRadioSelection(complicanzeForm.get('spasticitaSelected'), option, $event)"
                                    [value]="option" color="primary">
                                    {{ formatIntercorse(option.descrizione, 'f') | capitalizeFirst }}
                                </mat-radio-button>
                            </mat-radio-group>
                        </div>
                    </div>

                    <div class="col-12 mt-2">
                        <button mat-button type="button" class="p-0 note-button"
                            (click)="openPopupNote('noteSpasticita')">
                            <svg class="icon icon-primary">
                                <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                            </svg>
                            <span class="ms-1">Note</span>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Punteggio Scala Ashworth -->
            <div class="row mb-3 border-bottom pt-3">
                <div class="col-12 d-flex align-items-center pe-md-2 mb-md-0">
                    <div class="col-3">
                        <label class="field-label">
                            Punteggio Scala Ashworth
                            <span
                                *ngIf="datiDimissioneService.isRequired(complicanzeForm.get('punteggioScalaAshworth'))">*</span>
                        </label>
                    </div>
                    <div class="col-8 d-flex">
                        <mat-radio-group formControlName="punteggioScalaAshworth"
                            class="col-5 d-flex justify-content-between align-items-center">
                            <label class="field-label me-3">Stadio</label>
                            <mat-radio-button *ngFor="let stadio of optionsSpasticita" [value]="stadio" color="primary"
                                (click)="toggleRadioSelection(complicanzeForm, 'punteggioScalaAshworth', $event)">
                                {{ stadio }}
                            </mat-radio-button>
                        </mat-radio-group>
                    </div>
                </div>
                <div class="col-12 mt-2">
                    <button mat-button type="button" class="p-0 note-button"
                        (click)="openPopupNote('notePunteggioAshworth')">
                        <svg class="icon icon-primary">
                            <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                        </svg>
                        <span class="ms-1">Note</span>
                    </button>
                </div>
            </div>
        </div>

        <!-- Dolore -->
        <div class="row mb-3">
            <div class="col-12  pe-md-2 mb-3 mb-md-0">
                <label class="col-2 field-label">
                    Dolore<span *ngIf="doloreRequired">*</span>
                </label>
                @if (isDimissione) {
                <mat-radio-group formControlName="dolore">
                    <mat-radio-button (click)="toggleRadioSelection(complicanzeForm, 'dolore',true)" [value]="true"
                        color="primary">Si
                    </mat-radio-button>
                    <mat-radio-button (click)="toggleRadioSelection(complicanzeForm, 'dolore',false)" [value]="false"
                        class="ms-4" color="primary">No
                    </mat-radio-button>
                </mat-radio-group>
                } @else {
                <mat-radio-group [formControl]="doloreControl">
                    <mat-radio-button *ngFor="let option of optionsPresentiIntercorseSiNo?.siNo"
                        (click)="toggleRadioSelection(complicanzeForm, 'doloreSelected', $event)" [value]="option"
                        color="primary">
                        {{ formatIntercorse(option.descrizione, 'f') | capitalizeFirst }}
                    </mat-radio-button>
                </mat-radio-group>
                }
            </div>
        </div>

        <!-- Sede dolore -->
        <div class="row mb-3 border-bottom pb-3">
            <div class="col-12">
                <div>
                    <div class="col-6 row align-items-center">
                        <label class="field-label col-5 mb-3">Tipologia dolore<span
                                *ngIf="complicanzeForm.get(isDimissione ? 'dolore' : 'doloreSelected')?.value"></span></label>
                        <div class="col-2 ml-3 d-flex align-items-center">
                            <button mat-button type="button" class="p-0 note-button"
                                [disabled]="complicanzeForm.get('noteDolore')?.disabled"
                                (mousedown)="openPopupNote('noteDolore')">
                                <svg class="icon icon-primary">
                                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                </svg>
                                <span class="ms-1">Note</span>
                            </button>
                        </div>
                    </div>
                    <div class="row" [formArrayName]="isDimissione ? 'doloreDimissioneLista' : 'doloreLista'">
                        <div class="col-5 mb-2 pl-0" *ngFor="let control of doloreArray.controls; let i = index">
                            <div class="d-flex align-items-center" [formGroupName]="i">
                                <mat-checkbox (change)="toggleDolore(i, $event.checked)" class="pl-0 col-4"
                                    formControlName="selected" color="primary">
                                    {{ optionsDoloreComplicanze[i].descrizione | capitalizeFirst }}
                                </mat-checkbox>
                                <mat-radio-group *ngIf="complicanzeForm.controls['doloreLista']" class="pl-0 col-6"
                                    formControlName="idDizionario2">
                                    <mat-radio-button *ngFor="let option of optionsPresentiIntercorse"
                                        (click)="datiCliniciService.toggleRadioSelection(control, option, $event)"
                                        [value]="option" color="primary">
                                        {{ formatIntercorse(option.descrizione, 'm') | capitalizeFirst }}
                                    </mat-radio-button>
                                </mat-radio-group>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- INFEZIONE CONTAMINAZIONE MULTIREISTENTE -->
        <div class="row mb-3 border-bottom pb-3">
            <div class="pl-0 d-flex align-items-center col-12 mb-2">
                <div class="pl-0 col-4">
                    <label class="field-label">
                        Infezione-contaminazione multiresistente<span
                            *ngIf="infezioneContaminazioneMultiresistenteRequired">*</span>
                    </label>
                </div>
                <div class="pl-0 col-3">
                    @if (isDimissione) {
                    <mat-radio-group formControlName="infezioneContaminazioneMultiresistente">
                        <mat-radio-button
                            (click)="toggleRadioSelection(complicanzeForm, 'infezioneContaminazioneMultiresistente',true)"
                            [value]="true" color="primary">Si
                        </mat-radio-button>
                        <mat-radio-button
                            (click)="toggleRadioSelection(complicanzeForm, 'infezioneContaminazioneMultiresistente',false)"
                            [value]="false" color="primary">No
                        </mat-radio-button>
                    </mat-radio-group>
                    } @else {
                    <mat-radio-group formControlName="infezioneContaminazioneMultiresistenteSelected" class="d-flex">
                        <mat-radio-button *ngFor="let option of optionsPresentiIntercorseSiNo?.siNo"
                            (click)="toggleRadioSelection(complicanzeForm, 'infezioneContaminazioneMultiresistenteSelected', $event)"
                            [value]="option" color="primary">
                            {{ option.descrizione | capitalizeFirst }}
                        </mat-radio-button>
                    </mat-radio-group>
                    }
                </div>
                <div class="pl-0 col-4"
                    *ngIf="complicanzeForm.controls['infezioneContaminazioneMultiresistenteSelected']">
                    <div class="col-5">
                        <mat-radio-group formControlName="infezioneContaminazioneMultiresistente"
                            [required]="complicanzeForm.get('infezioneContaminazioneMultiresistenteSelected')?.value === true"
                            class="d-flex">
                            <mat-radio-button *ngFor="let option of optionsPresentiIntercorseSiNo?.presenti"
                                (click)="datiCliniciService.toggleRadioSelection(complicanzeForm.get('infezioneContaminazioneMultiresistente'), option, $event)"
                                [value]="option" color="primary">
                                {{ option.descrizione | capitalizeFirst }}
                            </mat-radio-button>
                        </mat-radio-group>
                    </div>
                </div>
                <div class="pl-0 col-2">
                    <button mat-button type="button" class="p-0 note-button"
                        (mousedown)="openPopupNote('noteMultiresistente')">
                        <svg class="icon icon-primary">
                            <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                        </svg>
                        <span class="ms-1">Note</span>
                    </button>
                </div>
            </div>
        </div>


        <!-- SEPSI -->
        <div class="pl-0 d-flex align-items-center col-12 mb-2">
            <div class="pl-0 col-4">
                <label class="field-label">
                    Sepsi<span *ngIf="sepsiRequired">*</span>
                </label>
            </div>

            <div class="pl-0 col-3">
                @if (isDimissione) {
                <mat-radio-group formControlName="sepsi">
                    <mat-radio-button (click)="toggleRadioSelection(complicanzeForm, 'sepsi',true)" [value]="true"
                        color="primary">Si
                    </mat-radio-button>
                    <mat-radio-button (click)="toggleRadioSelection(complicanzeForm, 'sepsi',false)" [value]="false"
                        color="primary">No
                    </mat-radio-button>
                </mat-radio-group>
                } @else {
                <mat-radio-group formControlName="sepsiSelected" class="d-flex">
                    <mat-radio-button *ngFor="let option of optionsPresentiIntercorseSiNo?.siNo" [value]="option"
                        (click)="toggleRadioSelection(complicanzeForm, 'sepsiSelected', $event)" color="primary">
                        {{ option.descrizione | capitalizeFirst }}
                    </mat-radio-button>
                </mat-radio-group>
                }
            </div>
            <div class="pl-0 col-4" *ngIf="complicanzeForm.controls['sepsiSelected']">
                <div class="col-5">
                    <mat-radio-group formControlName="sepsi"
                        [required]="complicanzeForm.get('sepsiSelected')?.value === true" class="d-flex">
                        <mat-radio-button *ngFor="let option of optionsPresentiIntercorseSiNo?.presenti"
                            [value]="option"
                            (click)="datiCliniciService.toggleRadioSelection(complicanzeForm.get('sepsi'), option, $event)"
                            color="primary">
                            {{ formatIntercorse(option.descrizione, 'f') | capitalizeFirst }}
                        </mat-radio-button>
                    </mat-radio-group>
                </div>
            </div>
            <div class="pl-0 col-2">
                <button mat-button type="button" class="p-0 note-button" (mousedown)="openPopupNote('noteSepsi')">
                    <svg class="icon icon-primary">
                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                    </svg>
                    <span class="ms-1">Note</span>
                </button>
            </div>
        </div>
    </div>
</form>