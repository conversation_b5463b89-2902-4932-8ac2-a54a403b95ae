<!-- Overlay per tutti i menu cronologia aperti -->
<div *ngFor="let menuKey of getOpenMenuKeys()" class="cronologia-menu-overlay">
  <div class="cronologia-menu" >
    
    <!-- Header del menu -->
    <div class="cronologia-menu-header">
      <h4>{{ getMenuState(menuKey).title }}</h4>
      <button mat-icon-button (click)="closeMenu(menuKey)" class="close-button">
        <span>&times;</span>
      </button>
    </div>
    
    <!-- Contenuto del menu -->
    <div class="cronologia-menu-content">
      <!-- Stato di caricamento -->
      <div *ngIf="getMenuState(menuKey).loading" class="loading-container">
        <div class="spinner"></div>
        <span>Caricamento...</span>
      </div>
      
      <!-- Nessun dato disponibile -->
      <div *ngIf="!getMenuState(menuKey).loading && getMenuState(menuKey).data.length === 0" class="no-data">
        Nessun dato disponibile
      </div>
      
      <!-- Lista cronologia -->
      <div *ngIf="!getMenuState(menuKey).loading && getMenuState(menuKey).data.length > 0" class="cronologia-items">
        <div *ngFor="let item of getMenuState(menuKey).data" class="cronologia-item">
          <div class="cronologia-item-header">
            <strong>{{ item.medicoCompilatore }}</strong>
            <span class="date">{{ item.dataUltimaModifica | date:'dd/MM/yyyy HH:mm' }}</span>
          </div>
          <div class="cronologia-item-body">
            <div class="presidio">
              <strong>Presidio:</strong> {{ item.presidioOspedaliero }}
            </div>
            <div class="tipo-scheda">
              <strong>Tipo Scheda:</strong> 
              <span *ngIf="menuKey.includes('dati_generali')">Dati Generali</span>
              <span *ngIf="!menuKey.includes('dati_generali')">{{ item.idTipoScheda }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
