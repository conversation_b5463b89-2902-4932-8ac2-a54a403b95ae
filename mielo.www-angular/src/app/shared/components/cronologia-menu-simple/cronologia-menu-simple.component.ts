import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Observable, of } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { ETipoScheda } from '../../enums/enum';
import { OutputHistoryDTO } from '../../interfaces/shared/shared.interface';
import { SchedaRicoveroService } from '../../../mielo/services/scheda-ricovero.service';
import {MatIcon} from "@angular/material/icon";

@Component({
  selector: 'app-cronologia-menu-simple',
  standalone: true,
  imports: [CommonModule, MatButtonModule, MatMenuModule, MatProgressSpinnerModule, MatIcon],
  template: `
      <button mat-button
              class="cronologia-button p-0"
              [matMenuTriggerFor]="cronologiaMenu"
              (click)="loadCronologia($event)"
              [disabled]="!idPaziente">
          Cronologia
      </button>

      <mat-menu
              id="cronologia-menu"
              xPosition="before"
              #cronologiaMenu="matMenu">
          <div (click)="$event.stopPropagation()">
              <!-- Header -->
              <div class="cronologia-header">
                  <mat-icon class="d-flex icon-color">access_time</mat-icon>
                  <h5>CRONOLOGIA MODIFICHE</h5>
                  <button mat-icon-button>
                      <mat-icon>close</mat-icon>
                  </button>
              </div>

              <!-- Loading -->
              <div *ngIf="loading" class="cronologia-loading">
                  <mat-spinner diameter="20"></mat-spinner>
                  <span>Caricamento...</span>
              </div>

              <!-- No data -->
              <div *ngIf="!loading && !data" class="cronologia-no-data">
                  Nessun dato disponibile
              </div>

              <div *ngIf="!loading && data" class="cronologia-items">
                  <div class="cronologia-item">
                      <span class="cronologia-item-header">Compilato da:</span>
                      <strong class="cronologia-item-body">{{ data?.medicoCompilatore }}</strong>
                  </div>
                  <div class="cronologia-item">
                      <span class="cronologia-item-header">Struttura:</span>
                      <strong class="cronologia-item-body">{{ data?.presidioOspedaliero }}</strong>
                  </div>
                  <div class="cronologia-item">
                      <span class="cronologia-item-header">Data e ora:</span>
                      <strong class="cronologia-item-body">{{ data?.dataUltimaModifica | date:'dd/MM/yyyy HH:mm' }}</strong>
                  </div>
              </div>
          </div>
      </mat-menu>
  `,
  styles: [`
      .cronologia-button {
          color: #0066cc;

          &:hover {
              background-color: rgba(0, 102, 204, 0.1);
          }
      }

      :host ::ng-deep .cronologia-mat-menu .mat-mdc-menu-panel {
          min-width: 400px;
          max-width: 600px;
          max-height: 400px;
      }

      .cronologia-header {
          border-bottom: 1px solid #ddd;
          display: flex;
          color: black !important;
          padding: 10px;
          justify-content: space-between;
          align-items: center;

          h5 {
              color: black !important;
              font-size: 18px !important;
              margin: 0 !important;
          }
      }

      .cronologia-loading {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 20px;
          gap: 8px;

          span {
              font-size: 14px;
              color: #666;
          }
      }

      .cronologia-no-data {
          padding: 20px;
          text-align: center;
          color: #666;
          font-size: 14px;
      }

      .cronologia-items {
          max-height: 300px;
          overflow-y: auto;
          padding: 16px;
          gap: 8px;
          display: flex;
          flex-direction: column;

          .cronologia-item {
              &:last-child {
                  border-bottom: none;
              }

              .cronologia-item-header {
                  display: flex;
                  flex-direction: column;
                  font-size: 16px;
              }

              .cronologia-item-body {
                  font-size: 16px;
                  font-weight: 600;

                  div {
                      margin-bottom: 4px;

                      strong {
                          color: #555;
                          margin-right: 4px;
                      }
                  }
              }
          }
      }

      @media (max-width: 768px) {
          :host ::ng-deep .cronologia-mat-menu .mat-mdc-menu-panel {
              min-width: 300px;
              max-width: 90vw;
          }
      }
  `]
})
export class CronologiaMenuSimpleComponent implements OnInit {
  @Input() idTipoScheda: ETipoScheda | null = null;
  @Input() idPaziente!: number;

  loading = false;
  data: OutputHistoryDTO | null = null;

  constructor(private schedaRicoveroService: SchedaRicoveroService) {}

  ngOnInit(): void {
    if (!this.idPaziente) {
      console.warn('CronologiaMenuSimpleComponent: idPaziente è richiesto');
    }
  }

  loadCronologia($event: MouseEvent): void {
    $event.stopPropagation();
    $event.preventDefault();
    if (!this.idPaziente) return;

    this.loading = true;
    this.data = null;

    this.schedaRicoveroService.getCronologia({
      idPaziente: this.idPaziente,
      idTipoScheda: this.idTipoScheda
    }).pipe(
      tap(res => {
        this.data = res
        this.loading = false;
      }),
      catchError(err => {
        console.error('Errore nel caricamento cronologia:', err);
        this.loading = false;
        this.data = null;
        return of(null);
      })
    ).subscribe();
  }

  getTipoSchedaLabel(): string {
    return this.idTipoScheda === null ? 'Dati Generali' : this.idTipoScheda.toString();
  }
}
