import { CommonModule } from '@angular/common';
import { Component, Input, OnInit } from '@angular/core';
import { MatButtonModule } from '@angular/material/button';
import { MatMenuModule } from '@angular/material/menu';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { Observable, of } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { ETipoScheda } from '../../enums/enum';
import { OutputHistoryDTO } from '../../interfaces/shared/shared.interface';
import { SchedaRicoveroService } from '../../../mielo/services/scheda-ricovero.service';

@Component({
  selector: 'app-cronologia-menu-simple',
  standalone: true,
  imports: [CommonModule, MatButtonModule, MatMenuModule, MatProgressSpinnerModule],
  template: `
    <button mat-button 
            class="cronologia-button p-0"
            [matMenuTriggerFor]="cronologiaMenu"
            (menuOpened)="loadCronologia()"
            [disabled]="!idPaziente">
      Cronologia
    </button>

    <mat-menu #cronologiaMenu="matMenu" class="cronologia-mat-menu">
      <div class="cronologia-menu-content" (click)="$event.stopPropagation()">
        <!-- Header -->
        <div class="cronologia-header">
          <h4>{{ getTitle() }}</h4>
        </div>

        <!-- Loading -->
        <div *ngIf="loading" class="cronologia-loading">
          <mat-spinner diameter="20"></mat-spinner>
          <span>Caricamento...</span>
        </div>

        <!-- No data -->
        <div *ngIf="!loading && data.length === 0" class="cronologia-no-data">
          Nessun dato disponibile
        </div>

        <!-- Data -->
        <div *ngIf="!loading && data.length > 0" class="cronologia-items">
          <div *ngFor="let item of data" class="cronologia-item">
            <div class="cronologia-item-header">
              <strong>{{ item.medicoCompilatore }}</strong>
              <span class="date">{{ item.dataUltimaModifica | date:'dd/MM/yyyy HH:mm' }}</span>
            </div>
            <div class="cronologia-item-body">
              <div><strong>Presidio:</strong> {{ item.presidioOspedaliero }}</div>
              <div><strong>Tipo Scheda:</strong> {{ getTipoSchedaLabel() }}</div>
            </div>
          </div>
        </div>
      </div>
    </mat-menu>
  `,
  styles: [`
    .cronologia-button {
      color: #0066cc;
      
      &:hover {
        background-color: rgba(0, 102, 204, 0.1);
      }
    }

    :host ::ng-deep .cronologia-mat-menu .mat-mdc-menu-panel {
      min-width: 400px;
      max-width: 500px;
      max-height: 400px;
    }

    .cronologia-menu-content {
      padding: 0;
    }

    .cronologia-header {
      padding: 12px 16px;
      background-color: #f8f9fa;
      border-bottom: 1px solid #ddd;
      
      h4 {
        margin: 0;
        font-size: 14px;
        font-weight: 600;
        color: #333;
      }
    }

    .cronologia-loading {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px;
      gap: 8px;
      
      span {
        font-size: 14px;
        color: #666;
      }
    }

    .cronologia-no-data {
      padding: 20px;
      text-align: center;
      color: #666;
      font-size: 14px;
    }

    .cronologia-items {
      max-height: 300px;
      overflow-y: auto;
      
      .cronologia-item {
        padding: 12px 16px;
        border-bottom: 1px solid #eee;
        
        &:last-child {
          border-bottom: none;
        }
        
        &:hover {
          background-color: #f8f9fa;
        }
        
        .cronologia-item-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
          
          strong {
            font-size: 14px;
            color: #333;
          }
          
          .date {
            font-size: 12px;
            color: #666;
          }
        }
        
        .cronologia-item-body {
          font-size: 13px;
          
          div {
            margin-bottom: 4px;
            
            strong {
              color: #555;
              margin-right: 4px;
            }
          }
        }
      }
    }

    @media (max-width: 768px) {
      :host ::ng-deep .cronologia-mat-menu .mat-mdc-menu-panel {
        min-width: 300px;
        max-width: 90vw;
      }
    }
  `]
})
export class CronologiaMenuSimpleComponent implements OnInit {
  @Input() idTipoScheda: ETipoScheda | null = null;
  @Input() idPaziente!: number;

  loading = false;
  data: OutputHistoryDTO[] = [];

  constructor(private schedaRicoveroService: SchedaRicoveroService) {}

  ngOnInit(): void {
    if (!this.idPaziente) {
      console.warn('CronologiaMenuSimpleComponent: idPaziente è richiesto');
    }
  }

  loadCronologia(): void {
    if (!this.idPaziente) return;

    this.loading = true;
    this.data = [];

    this.schedaRicoveroService.getCronologia({
      idPaziente: this.idPaziente,
      idTipoScheda: this.idTipoScheda
    }).pipe(
      tap(res => {
        this.data = Array.isArray(res) ? res : [res];
        this.loading = false;
      }),
      catchError(err => {
        console.error('Errore nel caricamento cronologia:', err);
        this.loading = false;
        this.data = [];
        return of([]);
      })
    ).subscribe();
  }

  getTitle(): string {
    if (this.idTipoScheda === null) {
      return 'Cronologia Dati Generali';
    }

    switch (this.idTipoScheda) {
      case ETipoScheda.VALUTAZIONE_IN_INGRESSO:
        return 'Cronologia Valutazione in Ingresso';
      case ETipoScheda.NECESSITA_ASSISTENZIALI_IN_INGRESSO:
        return 'Cronologia Necessità Assistenziali';
      case ETipoScheda.QUADRO_NEUROLOGICO:
        return 'Cronologia Quadro Neurologico';
      case ETipoScheda.EZIOLOGIA:
        return 'Cronologia Eziologia';
      case ETipoScheda.LESIONE_E_TRATTAMENTO:
        return 'Cronologia Lesione e Trattamento';
      case ETipoScheda.SETTING_RIABILITATIVO:
        return 'Cronologia Setting Riabilitativo';
      case ETipoScheda.COMPLICANZE:
        return 'Cronologia Complicanze';
      default:
        return 'Cronologia';
    }
  }

  getTipoSchedaLabel(): string {
    return this.idTipoScheda === null ? 'Dati Generali' : this.idTipoScheda.toString();
  }
}
