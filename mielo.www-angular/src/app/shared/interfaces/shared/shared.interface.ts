import { DizionarioModel } from "../scheda-ricovero.interface";

export interface CheckBoxSempliceModel {
    idDizionario: number;
}

export interface CheckBoxNumeroModel {
    idDizionario: number;
    numero: number;
}

export interface CheckBoxDataModel {
    idDizionario: number;
    data: Date;
}

export interface CheckBoxDictionaryModel {
    idDizionario1: number;
    idDizionario2?: number;
}

export interface InterventoChirurgicoModel {
    idScheda: number;
    idIntervento: number | null;
    dataOraInterventoChirurgico: Date;
    tipoInterventoChirurgicoLesione: DizionarioModel;
    noteInterventoChirurgico: string;
}

export interface UnitaOperativaModel {
    codice: string;
    idUnitaOperativa: number;
    nome: string;
}

export interface StruttureRiabilitativeModel {
    descrizioneLivello2: string;
    codLivello2: number;
}

export interface ComplicanzeRespiratorieModel {
    complicanzaRespiratoria: DizionarioModel;
    fkIdScheda: number;
    presenteIntercorsa: DizionarioModel;
}

export interface ComplicanzeUrologicheModel {
    complicanzaUrologica: DizionarioModel;
    fkIdScheda: number;
    presenteIntercorsa: DizionarioModel;
}

export interface DoloreModel {
    dolore: DizionarioModel;
    fkIdScheda: number;
    presenteIntercorsaDolore: DizionarioModel;
}

export interface OssificazioneModel {
    fkIdScheda: number;
    limitante: DizionarioModel;
    sedeOssificazione: DizionarioModel;
}

export interface PressioneModel {
    fkIdScheda: number;
    sedeLesionePressione: DizionarioModel;
    stadio: number;
}

export interface RachideModel {
    altroRachide: DizionarioModel;
    fkIdScheda: number;
    presenteIntercorsaRachide: DizionarioModel;
}

export interface TrombosiModel {
    fkIdScheda: number;
    sedeTrombosi: DizionarioModel;
}

export interface OutputHistoryDTO {
  idTipoScheda: number;
  medicoCompilatore: string;
  dataUltimaModifica: string;
  schedaClinica: {};
  presidioOspedaliero: string;
}