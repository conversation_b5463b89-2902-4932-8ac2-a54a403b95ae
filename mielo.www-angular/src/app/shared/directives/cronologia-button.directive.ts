import { Directive, ElementRef, HostListener, Input } from '@angular/core';
import { ETipoScheda } from '../enums/enum';
import { CronologiaMenuService } from '../services/cronologia-menu.service';

@Directive({
  selector: '[appCronologiaButton]',
  standalone: true
})
export class CronologiaButtonDirective {
  @Input('appCronologiaButton') idTipoScheda: ETipoScheda | null = null;
  @Input() idPaziente!: number | null;

  constructor(
    private elementRef: ElementRef<HTMLElement>,
    private cronologiaMenuService: CronologiaMenuService
  ) {
    // Aggiungi la classe CSS al bottone
    this.elementRef.nativeElement.classList.add('cronologia-button');
  }

  @HostListener('click', ['$event'])
  onClick(event: Event): void {
    event.stopPropagation();
    event.preventDefault();

    if (this.idPaziente && this.idTipoScheda !== undefined) {
      this.cronologiaMenuService.openCronologiaMenu(
        this.idPaziente,
        this.idTipoScheda,
        this.elementRef.nativeElement
      ).subscribe();
    } else {
      console.warn('CronologiaButtonDirective: idPaziente o idTipoScheda mancanti', {
        idPaziente: this.idPaziente,
        idTipoScheda: this.idTipoScheda
      });
    }
  }
}
