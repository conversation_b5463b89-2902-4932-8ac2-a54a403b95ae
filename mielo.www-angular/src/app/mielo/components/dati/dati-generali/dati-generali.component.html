<div class="container-data pt-4">
  <h3 class="ml-3">Dati generali</h3>
  <mat-accordion>
    <mat-expansion-panel expanded>
      <mat-expansion-panel-header>
        <mat-panel-title class="d-flex justify-content-between">
          <span class="panel-title">DATI GENERALI</span>
            <div *ngIf="datiGenerali?.evento?.tipoEvento?.idTipoEvento === TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO.idTipoEvento"
                 class="d-flex position-relative">
                <button mat-button type="button" class="p-0 cronologia-button"
                        (click)="getCronologia($event)">
                  Cronologia
                </button>

                <!-- Menu custom cronologia -->
                <div *ngIf="cronologiaMenuOpen" class="cronologia-menu">
                  <div class="cronologia-menu-header">
                    <h4>Cronologia Dati Generali</h4>
                    <button mat-icon-button (click)="closeCronologiaMenu()" class="close-button">
                      <span>&times;</span>
                    </button>
                  </div>

                  <div class="cronologia-menu-content">
                    <div *ngIf="cronologiaLoading" class="loading-container">
                      <div class="spinner"></div>
                      <span>Caricamento...</span>
                    </div>

                    <div *ngIf="!cronologiaLoading && cronologiaData.length === 0" class="no-data">
                      Nessun dato disponibile
                    </div>

                    <div *ngIf="!cronologiaLoading && cronologiaData.length > 0" class="cronologia-items">
                      <div *ngFor="let item of cronologiaData" class="cronologia-item">
                        <div class="cronologia-item-header">
                          <strong>{{ item.medicoCompilatore }}</strong>
                          <span class="date">{{ item.dataUltimaModifica | date:'dd/MM/yyyy HH:mm' }}</span>
                        </div>
                        <div class="cronologia-item-body">
                          <div class="presidio">
                            <strong>Presidio:</strong> {{ item.presidioOspedaliero }}
                          </div>
                          <div class="tipo-scheda">
                            <strong>Tipo Scheda:</strong> Dati Generali
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
            </div>
        </mat-panel-title>
      </mat-expansion-panel-header>

      <form [formGroup]="datiGeneraliForm" class="box-dati">
        <div class="mt-4">
          <div class="row">
            <div class="col-12 col-md-4 mb-3">
              <mat-label class="text-overflow">Medico compilatore</mat-label>
              <div
                class="value-dati">
                  {{medicoCompilatore}}
              </div>
            </div>
            <div class="col-12 col-md-4 mb-3">
              <mat-label class="text-overflow">Ente</mat-label>
              <div class="value-dati">{{ente}}</div>
            </div>
            <div class="col-12 col-md-4 mb-3">
              <mat-label class="text-overflow">Presidio</mat-label>
              <div class="value-dati">{{ presidio }}</div>
            </div>
          </div>

          <div class="row mt-3">
            <div class="col-12 col-md-4 mb-3">
              <mat-label class="text-overflow">ID (nosologico di ricovero)</mat-label>
              <mat-form-field class="w-100 mt-2">
                <input matInput
                       formControlName="idNosologico"
                       placeholder="Inserisci (es. 12/345678)"
                       maxlength="9"/>
                <mat-error
                  *ngIf="datiGeneraliForm.get('idNosologico')?.errors?.['required'] && formTouched && datiGeneraliForm.get('idNosologico')?.touched">
                  Campo obbligatorio
                </mat-error>
                <mat-error
                  *ngIf="datiGeneraliForm.get('idNosologico')?.errors?.['lunghezzaInvalida'] && formTouched && datiGeneraliForm.get('idNosologico')?.touched">
                  L'ID deve essere di 9 caratteri
                </mat-error>
                <mat-error
                  *ngIf="datiGeneraliForm.get('idNosologico')?.errors?.['formatoInvalido'] && formTouched && datiGeneraliForm.get('idNosologico')?.touched">
                  Formato non valido. Utilizzare il formato NN/NNNNNN
                </mat-error>
              </mat-form-field>
            </div>
            <div class="col-12 col-md-4 mb-3">
              <mat-label class="text-overflow">Data di ricovero*</mat-label>
              <mat-form-field class="w-100 mt-2">
                <input required matInput formControlName="dataRicovero"
                       [matDatepicker]="pickerRicovero"
                       [max]="moment()"
                       placeholder="GG/MM/AAAA">
                <mat-datepicker-toggle matIconSuffix matSuffix [for]="pickerRicovero">
                  <mat-icon svgIcon="it-calendar" matDatepickerToggleIcon class="d-flex icon-calendar"></mat-icon>
                </mat-datepicker-toggle>
                <mat-datepicker #pickerRicovero></mat-datepicker>
                <mat-error>{{ getErrorData() }}</mat-error>
              </mat-form-field>
            </div>
            <div class="col-12 col-md-4 mb-3">
              <mat-label class="text-overflow">Ricovero in*</mat-label>
              <mat-form-field class="w-100 mt-2">
                <mat-select required
                            formControlName="unitaOperativa" placeholder="Seleziona"
                            [compareWith]="compareByIdUnitaOperativa">
                  <mat-option *ngFor="let unitaOperativa of unitaOperative" [value]="unitaOperativa">
                    {{ unitaOperativa.nome | capitalizeFirst }}
                  </mat-option>
                </mat-select>
                <mat-error
                  *ngIf="datiGeneraliForm.get('unitaOperativa')?.errors?.['required']">
                  Campo obbligatorio
                </mat-error>
              </mat-form-field>
            </div>
          </div>

          <div class="row mt-3">
            <div class="col-8 mb-3">
              <mat-label class="text-overflow">Reparto di ricovero</mat-label>
              <mat-form-field class="w-100 mt-2">
                <mat-select formControlName="repartoRicovero" placeholder="Seleziona">
                  <mat-option [value]="null"></mat-option>
                  <mat-option *ngFor="let option of ['Opzione 1', 'Opzione 2', 'Opzione 3']" [value]="option">
                    {{ option }}
                  </mat-option>
                </mat-select>
                <mat-error
                  *ngIf="datiGeneraliForm.get('repartoRicovero')?.invalid && formTouched && datiGeneraliForm.get('repartoRicovero')?.touched">
                  Campo obbligatorio
                </mat-error>
              </mat-form-field>
            </div>
            <div class="col-4 mb-3" *ngIf="showLesioneMielica">
              <mat-label class="text-overflow">Lesione mielica*</mat-label>
              <div class="row mt-2">
                <div class="col-12">
                  <mat-radio-group
                    formControlName="lesioneMielica" aria-label="Seleziona un'opzione"
                    class="d-flex flex-wrap">
                    <div class="d-flex align-items-center mr-3 mb-2">
                      <mat-radio-button *ngFor="let lesione of lesioni"
                                        [value]="lesione.idDizionario">
                        <span class="f-18 font-weight-medium">{{ lesione.descrizione | capitalizeFirst }}</span>
                      </mat-radio-button>
                    </div>
                    <div class="d-flex align-items-center mb-2">
                      <button mat-button type="button" class="p-0" (click)="openPopupNote()">
                        <svg class="icon mr-2 mb-1 icon-primary">
                          <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                        </svg>
                        <span class="pl-1 icon-primary">Note</span>
                      </button>
                    </div>
                  </mat-radio-group>
                  <mat-error
                    *ngIf="datiGeneraliForm.get('lesioneMielica')?.invalid && datiGeneraliForm.get('lesioneMielica')?.touched">
                    Campo obbligatorio
                  </mat-error>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </mat-expansion-panel>
  </mat-accordion>
</div>
