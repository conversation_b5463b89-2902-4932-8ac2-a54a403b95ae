import { CommonModule } from "@angular/common";
import { Component, HostListener, Input, OnC<PERSON><PERSON>, OnDestroy } from '@angular/core';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from "@angular/forms";
import { DateAdapter } from "@angular/material/core";
import { MatDatepickerModule } from "@angular/material/datepicker";
import { MatIconRegistry } from "@angular/material/icon";
import { MatInputModule } from "@angular/material/input";
import { DomSanitizer } from "@angular/platform-browser";
import { catchError, of, Subscription, tap } from "rxjs";
import { MaterialModule } from "../../../../core/material.module";
import { OperatoreService } from "../../../../core/services/operatore.service";
import { DecoderService } from "../../../../shared/services/decoder.service";
import { ModalService } from "../../../../shared/services/modal.service";
import { NosologicoValidator } from "../../../../shared/validators/dati-generali.validator";
import moment from "moment";
import {
  AggiungiSchedaRicoveroRequest,
  DizionarioModel, SchedaRicoveroModel,
  UnitaOperativaModel
} from "../../../../shared/interfaces/scheda-ricovero.interface";
import { takeUntilDestroyed } from "@angular/core/rxjs-interop";
import { SchedaRicoveroService } from "../../../services/scheda-ricovero.service";
import { decodeFromBase64 } from "../../../../shared/utils/utils";
import { CapitalizePipe } from "../../../../shared/pipes/capitalize.pipe";
import {ERROR_MESSAGE, ETipoScheda, StatoEventoEnum} from "../../../../shared/enums/enum";
import { TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO } from "../../../../shared/utils/const";
import { OutputHistoryDTO } from "../../../../shared/interfaces/shared/shared.interface";

const IT_CALENDAR = `<svg viewBox="0 0 24 24" id="it-calendar" xmlns="http://www.w3.org/2000/svg"><path d="M20.5 4H17V3h-1v1H8V3H7v1H3.5A1.5 1.5 0 002 5.5v13A1.5 1.5 0 003.5 20h17a1.5 1.5 0 001.5-1.5v-13A1.5 1.5 0 0020.5 4zm.5 14.5a.5.5 0 01-.5.5h-17a.5.5 0 01-.5-.5v-13a.5.5 0 01.5-.5H7v1h1V5h8v1h1V5h3.5a.5.5 0 01.5.5zM4 8h16v1H4z"/><path fill="none" d="M0 0h24v24H0z"/></svg>`;


@Component({
  selector: 'app-dati-generali',
  standalone: true,
  imports: [MaterialModule, CommonModule, ReactiveFormsModule, MatDatepickerModule, MatInputModule, CapitalizePipe],
  templateUrl: './dati-generali.component.html',
  styleUrl: './dati-generali.component.scss'
})
export class DatiGeneraliComponent implements OnDestroy, OnChanges {

  datiGeneraliForm: FormGroup;
  idNosologico: FormControl;
  dataRicovero: FormControl;
  unitaOperativa: FormControl;
  repartoRicovero: FormControl;
  lesioneMielica: FormControl;
  lesioneNote: FormControl;

  @Input() datiGenerali: SchedaRicoveroModel

  unitaOperative: UnitaOperativaModel[] = [];

  formTouched: boolean = false;

  protected readonly moment = moment;
  private datiGeneraliForm$: Subscription;
  protected readonly TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO = TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO;

  lesioni: DizionarioModel[];
  medicoCompilatore: string;
  ente: string;
  presidio: string;

  showLesioneMielica: boolean = true;
  private getCronologia$: Subscription;

  // Cronologia menu properties
  cronologiaMenuOpen: boolean = false;
  cronologiaData: OutputHistoryDTO[] = [];
  cronologiaLoading: boolean = false;

  constructor(public operatoreService: OperatoreService, iconRegistry: MatIconRegistry, sanitizer: DomSanitizer,
    private modalService: ModalService,
    public schedaRicService: SchedaRicoveroService,
    private decoderService: DecoderService,
    private dateAdapter: DateAdapter<any>) {

    this.decoderService.getDcodUnitaOperative().pipe(
      takeUntilDestroyed(),
      tap((data) => {
        this.unitaOperative = data;
      }),

      catchError((error) => {
        console.error('Errore durante il recupero delle unità operative:', error);
        return of([]);
      })
    ).subscribe();

    iconRegistry.addSvgIconLiteral('it-calendar', sanitizer.bypassSecurityTrustHtml(IT_CALENDAR));

    this.dateAdapter.setLocale('it-IT');

    this.idNosologico = new FormControl(null, [
      NosologicoValidator.format()
    ]);
    this.dataRicovero = new FormControl('', [Validators.required]);
    this.unitaOperativa = new FormControl(null, Validators.required);
    this.repartoRicovero = new FormControl(undefined);
    this.lesioneMielica = new FormControl('', Validators.required);
    this.lesioneNote = new FormControl('');

    if (this.schedaRicService.isNewEvent.getValue()) {
      this.lesioneMielica.disable();
      this.unitaOperativa.disable();
    }

    this.datiGeneraliForm = new FormGroup({
      'idNosologico': this.idNosologico,
      'dataRicovero': this.dataRicovero,
      'unitaOperativa': this.unitaOperativa,
      'repartoRicovero': this.repartoRicovero,
      'lesioneMielica': this.lesioneMielica,
      'lesioneNote': this.lesioneNote
    });
    this.datiGeneraliForm.valueChanges
      .pipe(
        takeUntilDestroyed(),
        tap((data) => {
          schedaRicService._areFormsCreateSchedaValidSubject$.next({
            ...schedaRicService._areFormsCreateSchedaValidSubject$.getValue(),
            formDatiGenerali: {
              valid: this.datiGeneraliForm.valid,
              pristine: this.datiGeneraliForm.pristine
            }
          });

          schedaRicService.schedaCreation$.next({
            ...schedaRicService.schedaCreation$.getValue(),
            scheda: {
              ...schedaRicService.schedaCreation$.getValue()?.scheda,
              ...data,
              lesioneMielica: this.lesioni?.find(l => l.idDizionario === (data.lesioneMielica ? data.lesioneMielica : this.datiGenerali?.scheda?.lesioneMielica?.idDizionario)) || null
            }
          } as AggiungiSchedaRicoveroRequest);
        })
      ).subscribe()

    this.decoderService.getDcodStatoLesione().pipe(
      takeUntilDestroyed(),
      tap(data => this.lesioni = data)
    ).subscribe()

    // se sono in creazione carico i dati dal subject in modo da non perderli quando cambio menu della sx
    this.datiGeneraliForm.patchValue({
      ...(schedaRicService.schedaCreation$?.getValue() as AggiungiSchedaRicoveroRequest)?.scheda,
      lesioneMielica: schedaRicService.schedaCreation$.getValue()?.scheda?.lesioneMielica?.idDizionario,
    }, { emitEvent: false })
    if (!schedaRicService.idSchedaSelectedSubject.getValue()) {
      // modo per capire quando torno da dati anagrafici a dat generale e fare check campi
      if (Object.keys(schedaRicService.schedaCreation$.getValue()?.paziente || {}).length) {
        this.datiGeneraliForm.markAllAsTouched()
      }
    }

    this.medicoCompilatore = `${this.operatoreService.getOperatore().nomeOperatore} ${this.operatoreService.getOperatore().cognomeOperatore}`
    this.ente = this.operatoreService.getOperatore().descLivello1Operatore || '';
    this.presidio = this.operatoreService.getOperatore().descLivello2Operatore || ''
  }


  ngOnChanges() {
    if (this.datiGenerali) { //update
      this.showLesioneMielica = this.datiGenerali.evento.tipoEvento.idTipoEvento !== TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO.idTipoEvento;
      const dataRicovero = moment(this.datiGenerali.scheda.dataRicovero)
      const repartoRicovero = this.datiGenerali.scheda.repartoRicovero
      const lesioneMielica = this.datiGenerali.scheda.lesioneMielica.idDizionario
      const unitaOperativa = this.datiGenerali.scheda.unitaOperativa
      const lesioneNote = decodeFromBase64(this.datiGenerali.scheda.lesioneNote || '')
      const idNosologico = this.schedaRicService.schedaCreation$.getValue()?.scheda?.idNosologico

      if (!this.schedaRicService.schedaCreation$.getValue()?.scheda) {
        this.datiGeneraliForm.get('lesioneMielica')?.disable({ emitEvent: false });
        this.datiGeneraliForm.get('unitaOperativa')?.disable({ emitEvent: false });
        this.schedaRicService.schedaCreation$.next({
          ...this.schedaRicService.schedaCreation$.getValue(),
          scheda: {
            ...this.schedaRicService.schedaCreation$.getValue()?.scheda,
            ...this.datiGenerali.scheda,
            lesioneMielica: this.lesioni?.find(l => l.idDizionario === this.datiGenerali.scheda.lesioneMielica.idDizionario) || undefined
          }
        } as AggiungiSchedaRicoveroRequest);

        this.datiGeneraliForm.patchValue({
          ...this.datiGenerali.scheda,
          dataRicovero,
          lesioneMielica,
          unitaOperativa,
          lesioneNote
        });
      } else if (!this.datiGenerali.scheda.id && this.datiGenerali.evento.tipoEvento.idTipoEvento === TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO.idTipoEvento) {

        if (this.datiGenerali.scheda.id !== this.schedaRicService.schedaCreation$.getValue()?.scheda.id) {
          this.schedaRicService.schedaCreation$.next({
            ...this.schedaRicService.schedaCreation$.getValue(),
            scheda: {
              ...this.schedaRicService.schedaCreation$.getValue()?.scheda,
              ...this.datiGenerali.scheda,
              lesioneMielica: this.lesioni?.find(l => l.idDizionario === this.datiGenerali.scheda.lesioneMielica.idDizionario) || undefined
            }
          } as AggiungiSchedaRicoveroRequest);
        }

        this.datiGeneraliForm.patchValue({
          idNosologico,
          lesioneMielica,
          unitaOperativa,
          dataRicovero,
          repartoRicovero
        });
        if (lesioneMielica) this.datiGeneraliForm.get('lesioneMielica')?.disable({ emitEvent: false });
        if (unitaOperativa) this.datiGeneraliForm.get('unitaOperativa')?.disable({ emitEvent: false });
      }

      if (this.datiGenerali.evento.stato && this.datiGenerali.evento.stato.idDizionario === StatoEventoEnum.CHIUSO) {
        this.medicoCompilatore = this.datiGenerali.nomeCognomeMedico;
        this.ente = this.datiGenerali.ente;
        this.presidio = this.datiGenerali.presidioOspedaliero;
      } else {
        this.medicoCompilatore = `${this.operatoreService.getOperatore().nomeOperatore} ${this.operatoreService.getOperatore().cognomeOperatore}`;
        this.ente = this.operatoreService.getOperatore().descLivello1Operatore || '';
        this.presidio = this.operatoreService.getOperatore().descLivello2Operatore || '';
      }

      if (lesioneMielica) this.datiGeneraliForm.get('lesioneMielica')?.disable({ emitEvent: false });
      if (unitaOperativa) this.datiGeneraliForm.get('unitaOperativa')?.disable({ emitEvent: false });

      if (this.datiGenerali.evento.stato && this.datiGenerali.evento.stato?.idDizionario !== StatoEventoEnum.IN_LAVORAZIONE) {
        Object.keys(this.datiGeneraliForm.controls).forEach(key => {
          this.datiGeneraliForm.get(key)?.disable({ emitEvent: false });
        })
      }
    }
    else if (!this.datiGenerali && this.schedaRicService.schedaCreation$.getValue()?.scheda) {
      this.datiGeneraliForm.patchValue({
        ...(this.schedaRicService.schedaCreation$?.getValue() as AggiungiSchedaRicoveroRequest)?.scheda,
        lesioneMielica: this.schedaRicService.schedaCreation$.getValue()?.scheda?.lesioneMielica?.idDizionario,
      }, { emitEvent: false })

    }
  }

  getCronologia(event?: Event) {
    if (event) {
      event.stopPropagation();
      event.preventDefault();
    }

    this.cronologiaLoading = true;
    this.cronologiaMenuOpen = true;

    this.getCronologia$ = this.schedaRicService.getCronologia({
      idPaziente: this.datiGenerali.idPaziente,
      idTipoScheda: null // scheda dati generali
    })
        .pipe(
            tap(res => {
              console.log(res);
              // Assuming the response is an array or single object
              this.cronologiaData = Array.isArray(res) ? res : [res];
              this.cronologiaLoading = false;
            })
        )
        .subscribe({
          error: (err) => {
            console.error('Errore nel caricamento cronologia:', err);
            this.cronologiaLoading = false;
            this.cronologiaData = [];
          }
        });
  }

  closeCronologiaMenu() {
    this.cronologiaMenuOpen = false;
    this.cronologiaData = [];
  }

  @HostListener('document:click', ['$event'])
  onDocumentClick(event: Event) {
    const target = event.target as HTMLElement;
    const cronologiaButton = target.closest('.cronologia-button');
    const cronologiaMenu = target.closest('.cronologia-menu');

    // Chiudi il menu se il click non è sul bottone o nel menu
    if (!cronologiaButton && !cronologiaMenu && this.cronologiaMenuOpen) {
      this.closeCronologiaMenu();
    }
  }

  compareByIdUnitaOperativa(option: any, value: any): boolean {
    if (option === null || value === null) {
      return false;
    }

    return option?.idUnitaOperativa === value?.idUnitaOperativa;
  }

  openPopupNote() {
    if (this.schedaRicService.idSchedaSelectedSubject.getValue()) { // se è update apro popup in modalita disabled
      let configNote = this.modalService.createNoteParam(this.lesioneNote.value, true);

      this.modalService.note(configNote)?.subscribe();
    } else {
      let configNote = this.modalService.createNoteParam(this.lesioneNote.value);

      this.modalService.note(configNote)?.subscribe((res) => {
        if (res != this.lesioneNote.value) {
          this.lesioneNote.setValue(res);
          this.lesioneNote.markAsDirty();
          this.lesioneNote.updateValueAndValidity();
          this.formTouched = true;
        }
      });
    }
  }


  getErrorData() {
    return this.datiGeneraliForm.get('dataRicovero')?.hasError('required') ? 'Campo obbligatorio' :
      this.datiGeneraliForm.get('dataRicovero')?.hasError('matDatepickerMax') ? ERROR_MESSAGE.MAX_DATE : 'Campo non valido'
  }

  ngOnDestroy() {
    this.datiGeneraliForm$?.unsubscribe()
    this.getCronologia$?.unsubscribe()
  }

}
