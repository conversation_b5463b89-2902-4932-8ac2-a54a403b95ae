import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatNativeDateModule } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatIconRegistry } from '@angular/material/icon';
import { DomSanitizer } from '@angular/platform-browser';
import { debounceTime, distinctUntilChanged, forkJoin, Subject, Subscription, takeUntil } from 'rxjs';
import { MaterialModule } from '../../../../../core/material.module';
import { TipoRicoveroEnum } from '../../../../../shared/enums/tipo-ricovero.enum';
import { SchedaQuadroNeurologicoModel } from '../../../../../shared/interfaces/dati-clinici.interface';
import { DizionarioModel } from '../../../../../shared/interfaces/scheda-ricovero.interface';
import { DecoderService } from '../../../../../shared/services/decoder.service';
import { ModalService } from '../../../../../shared/services/modal.service';
import { convertToDate } from '../../../../../shared/utils/utils';
import { DatiCliniciService } from '../../../../services/dati-clinici.service';
import { SchedaRicoveroService } from '../../../../services/scheda-ricovero.service';
import { DatiDimissioneService } from "../../../../services/dati-dimissione.service";
import { SchedaQuadroNeurologicoDimissioneModel } from "../../../../../shared/interfaces/dati-dimissione.interface";
import moment from "moment";
import { ERROR_MESSAGE } from "../../../../../shared/enums/enum";
import { CapitalizePipe } from '../../../../../shared/pipes/capitalize.pipe';

const IT_CALENDAR = `<svg viewBox="0 0 24 24" id="it-calendar" xmlns="http://www.w3.org/2000/svg"><path d="M20.5 4H17V3h-1v1H8V3H7v1H3.5A1.5 1.5 0 002 5.5v13A1.5 1.5 0 003.5 20h17a1.5 1.5 0 001.5-1.5v-13A1.5 1.5 0 0020.5 4zm.5 14.5a.5.5 0 01-.5.5h-17a.5.5 0 01-.5-.5v-13a.5.5 0 01.5-.5H7v1h1V5h8v1h1V5h3.5a.5.5 0 01.5.5zM4 8h16v1H4z"/><path fill="none" d="M0 0h24v24H0z"/></svg>`;

@Component({
  selector: 'app-quadro-neurologico',
  standalone: true,
  imports: [
    CommonModule,
    MaterialModule,
    ReactiveFormsModule,
    MatDatepickerModule,
    MatNativeDateModule,
    CapitalizePipe
  ],
  templateUrl: './quadro-neurologico.component.html',
  styleUrl: './quadro-neurologico.component.scss'
})
export class QuadroNeurologicoComponent implements OnInit, OnDestroy {

  @Output() expandedChange = new EventEmitter<boolean>();
  @Input() isDimissione: boolean = false;
  @Input() idReparto: TipoRicoveroEnum;

  @Input() readOnly: boolean = false;

  protected readonly ERROR_MESSAGE = ERROR_MESSAGE;
  protected readonly moment = moment;
  quadroNeurologicoForm: FormGroup;
  private datiQuadroNeurologico: SchedaQuadroNeurologicoModel | SchedaQuadroNeurologicoDimissioneModel
  validazioneScimAttiva = false;

  private readonly destroy$ = new Subject<void>();

  get formGroup(): FormGroup {
    return this.quadroNeurologicoForm;
  }

  optionsLivelliVertebre: Array<DizionarioModel> = [];
  optionsAsia: Array<DizionarioModel> = [];
  optionsBoolean: Array<DizionarioModel> = [];

  private subscriptions: Subscription[] = [];

  constructor(
    private fb: FormBuilder,
    private schedaRicoveroService: SchedaRicoveroService,
    public datiCliniciService: DatiCliniciService,
    private decoderService: DecoderService,
    private modalService: ModalService,
    iconRegistry: MatIconRegistry,
    sanitizer: DomSanitizer,
    private datiDimissioneService: DatiDimissioneService
  ) {
    iconRegistry.addSvgIconLiteral('it-calendar', sanitizer.bypassSecurityTrustHtml(IT_CALENDAR));

    this.quadroNeurologicoForm = this.fb.group({
      dataValutazione: [null, [Validators.required]],
      asia: [null],
      dataValutazioneAsia: [null],
      ais: [''],
      livelloNeurologico: [null],
      punteggioLivelloNeurologico: [{ value: '', disabled: true }, [
        Validators.pattern('^[0-9]{1,2}$')
      ]],
      livelloMotorioDx: [null],
      punteggioLivelloMotorioDx: [{ value: '', disabled: true }, [
        Validators.pattern('^[0-9]{1,2}$')
      ]],
      livelloMotorioSx: [null],
      punteggioLivelloMotorioSx: [{ value: '', disabled: true }, [
        Validators.pattern('^[0-9]{1,2}$')
      ]],
      livelloSensitivoDx: [null],
      punteggioLivelloSensitivoDx: [{ value: '', disabled: true }, [
        Validators.pattern('^[0-9]{1,2}$')
      ]],
      livelloSensitivoSx: [null],
      punteggioLivelloSensitivoSx: [{ value: '', disabled: true }, [
        Validators.pattern('^[0-9]{1,2}$')
      ]],
      noteAsia: [''],
      noteAis: [''],
      notePunteggi: [''],
      scim: [null, Validators.required],
      scimCuraSe: [{ value: '', disabled: true }],
      scimRespirazione: [{ value: '', disabled: true }],
      scimMobilita: [{ value: '', disabled: true }]
    });
  }

  ngOnInit() {
    forkJoin({
      vertebre: this.decoderService.getDcodVertebre(),
      asia: this.decoderService.getDcodSiNoNonValutabile(),
      boolean: this.decoderService.getDcodBoolean()
    }).subscribe(options => {
      this.optionsLivelliVertebre = options.vertebre;
      this.optionsAsia = options.asia;
      this.optionsBoolean = options.boolean;

      let dati;
      if (!this.isAcutiDimissione()) {
        dati = this.datiCliniciService.getDatiQuadroNeurologicoValue();
      } else {
        dati = this.datiDimissioneService.getQuadroNeurologicoValue();
        this.quadroNeurologicoForm.get('dataValutazione')?.removeValidators([Validators.required]);
        this.quadroNeurologicoForm.get('dataValutazione')?.updateValueAndValidity();
      }

      if (dati) {
        this.populateForm(dati);
        this.datiQuadroNeurologico = dati;
      }

      if (this.readOnly) {
        Object.keys(this.quadroNeurologicoForm.controls).forEach(controlName => {
          if (!controlName.toLowerCase().includes('nota')) {
            this.quadroNeurologicoForm.get(controlName)?.disable({ emitEvent: false });
          }
        });
      } else {
        this.disableInitialFields();
        this.initFieldDependencies();
      }

    });

    const formChanges = this.quadroNeurologicoForm.valueChanges.pipe(
      debounceTime(1),
      distinctUntilChanged((a, b) => JSON.stringify(a) === JSON.stringify(b)),
    ).subscribe(() => {
      const formValue = this.quadroNeurologicoForm.getRawValue()
      this.quadroNeurologicoForm.updateValueAndValidity()

      if (this.isAcutiDimissione()) {
        this.datiDimissioneService.setQuadroNeurologico(this.mapFormToModel(formValue) as SchedaQuadroNeurologicoDimissioneModel);
        this.datiDimissioneService.setQuadroNeurologicoValido(this.quadroNeurologicoForm.valid);
      } else {
        this.datiCliniciService.setDatiQuadroNeurologico(this.mapFormToModel(formValue));
        this.datiCliniciService.setDatiQuadroNeurologicoValido(this.quadroNeurologicoForm.valid);
      }
    });

    this.subscriptions.push(formChanges);

    const scimControl = this.quadroNeurologicoForm.get('scim');

    if (scimControl) {
      scimControl.valueChanges.pipe(
        takeUntil(this.destroy$)
      ).subscribe(value => {
          this.validazioneScimAttiva = value;
      });
    }
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  isAcutiDimissione(): boolean {
    return this.isDimissione && this.idReparto === TipoRicoveroEnum.ACUTI;
  }

  /**
   * Imposta lo stato iniziale di abilitazione/disabilitazione dei campi
   */
  disableInitialFields() {
    if (!this.isAcutiDimissione()) {
      this.quadroNeurologicoForm.get('asia')?.enable();
      this.quadroNeurologicoForm.get('asia')?.setValidators([Validators.required]);
    } else if (this.isDimissione && this.idReparto !== TipoRicoveroEnum.ACUTI) {
      this.quadroNeurologicoForm.get('asia')?.enable();
      this.quadroNeurologicoForm.get('asia')?.setValidators([Validators.required]);
      this.quadroNeurologicoForm.get('dataValutazione')?.enable();
      this.quadroNeurologicoForm.get('dataValutazione')?.setValidators([Validators.required]);
    }

    this.updateAsiaFieldsStatus();
    this.updatePunteggioFieldsStatus();
  }

  // Metodo per controllare la disabilitazione del SOLO pulsante NOTE, non del campo di testo interno
  isNoteDisabled(controlName: string): boolean {
    const control = this.quadroNeurologicoForm.get(controlName);
    return !control?.value && !!control?.disabled;
  }

  /**
   * Resetta i campi SCIM quando necessario
   */
  resetScimRelatedFields() {
    const scimControls = [
      'scim', 'scimCuraSe', 'scimRespirazione', 'scimMobilita'
    ];

    scimControls.forEach(controlName => {
      const control = this.quadroNeurologicoForm.get(controlName);
      if (control) {
        control.setValue(controlName === 'scim' ? null : '');
        control.disable();
        control.updateValueAndValidity();
      }
    });
  }

  /**
   * Configura le dipendenze tra campi del form e le reazioni ai cambiamenti
   */
  initFieldDependencies() {
    this.subscriptions.push(
      this.quadroNeurologicoForm.get('dataValutazione')?.valueChanges.subscribe(value => {
        if (value) {
          // ASIA è sempre abilitato, ma aggiungiamo i validatori
          this.quadroNeurologicoForm.get('asia')?.setValidators([Validators.required]);
        } else {
          // ASIA resta abilitato anche senza data
          this.quadroNeurologicoForm.get('asia')?.setValidators([Validators.required]);
        }

        this.quadroNeurologicoForm.get('asia')?.updateValueAndValidity();
      }) || new Subscription()
    );

    // ASIA controlla tutti i campi correlati
    this.subscriptions.push(
      this.quadroNeurologicoForm.get('asia')?.valueChanges.subscribe(() => {
        this.updateAsiaFieldsStatus();
      }) || new Subscription()
    );

    // Livello neurologico e punteggio
    this.subscriptions.push(
      this.quadroNeurologicoForm.get('livelloNeurologico')?.valueChanges.subscribe(value => {
        const punteggioControl = this.quadroNeurologicoForm.get('punteggioLivelloNeurologico');
        if (value) {
          punteggioControl?.enable();
          punteggioControl?.addValidators(Validators.required)
        } else {
          punteggioControl?.disable();
          punteggioControl?.removeValidators(Validators.required)
          punteggioControl?.setValue('');
        }
        // Reset e update validità solo in dimissione ACUTI
        if (this.isAcutiDimissione() && !value) {
          punteggioControl?.reset();
          punteggioControl?.setErrors(null);
        }
        punteggioControl?.updateValueAndValidity();
      }) || new Subscription()
    );

    // Livello motorio dx e punteggio
    this.subscriptions.push(
      this.quadroNeurologicoForm.get('livelloMotorioDx')?.valueChanges.subscribe(value => {
        const punteggioControl = this.quadroNeurologicoForm.get('punteggioLivelloMotorioDx');
        if (value) {
          punteggioControl?.enable();
          punteggioControl?.addValidators(Validators.required);
        } else {
          punteggioControl?.disable();
          // punteggioControl?.addValidators(Validators.required);
          punteggioControl?.setValue('');
        }
        punteggioControl?.updateValueAndValidity();
      }) || new Subscription()
    );

    // Livello motorio sx e punteggio
    this.subscriptions.push(
      this.quadroNeurologicoForm.get('livelloMotorioSx')?.valueChanges.subscribe(value => {
        const punteggioControl = this.quadroNeurologicoForm.get('punteggioLivelloMotorioSx');
        if (value) {
          punteggioControl?.enable();
          punteggioControl?.addValidators(Validators.required);
        } else {
          punteggioControl?.disable();
          punteggioControl?.setValue('');
        }
        punteggioControl?.updateValueAndValidity();
      }) || new Subscription()
    );

    // Livello sensitivo dx e punteggio
    this.subscriptions.push(
      this.quadroNeurologicoForm.get('livelloSensitivoDx')?.valueChanges.subscribe(value => {
        const punteggioControl = this.quadroNeurologicoForm.get('punteggioLivelloSensitivoDx');
        if (value) {
          punteggioControl?.enable();
          punteggioControl?.addValidators(Validators.required);
        } else {
          punteggioControl?.disable();
          punteggioControl?.setValue('');
        }
        punteggioControl?.updateValueAndValidity();
      }) || new Subscription()
    );

    // Livello sensitivo sx e punteggio
    this.subscriptions.push(
      this.quadroNeurologicoForm.get('livelloSensitivoSx')?.valueChanges.subscribe(value => {
        const punteggioControl = this.quadroNeurologicoForm.get('punteggioLivelloSensitivoSx');
        if (value) {
          punteggioControl?.enable();
          punteggioControl?.addValidators(Validators.required);
        } else {
          punteggioControl?.disable();
          punteggioControl?.setValue('');
        }
        punteggioControl?.updateValueAndValidity();
      }) || new Subscription()
    );

    // SCIM e campi correlati
    this.subscriptions.push(
      this.quadroNeurologicoForm.get('scim')?.valueChanges.subscribe(value => {

        const scimControls = [
          this.quadroNeurologicoForm.get('scimCuraSe'),
          this.quadroNeurologicoForm.get('scimRespirazione'),
          this.quadroNeurologicoForm.get('scimMobilita')
        ];

        if (value === true) {
          scimControls.forEach(control => {
            control?.enable();
            control?.setValidators([
              Validators.required,
              Validators.pattern('^[0-9]{1,2}$')
            ]);
          });

          this.quadroNeurologicoForm.get('scimCuraSe')?.addValidators([
            Validators.min(0),
            Validators.max(20)
          ]);

          this.quadroNeurologicoForm.get('scimRespirazione')?.addValidators([
            Validators.min(0),
            Validators.max(40)
          ]);

          this.quadroNeurologicoForm.get('scimMobilita')?.addValidators([
            Validators.min(0),
            Validators.max(40)
          ]);
        } else {
          scimControls.forEach(control => {
            control?.disable();
            control?.clearValidators();
            control?.setValue('');
          });
        }

        scimControls.forEach(control => {
          control?.updateValueAndValidity();
        });
      }) || new Subscription()
    );
  }

  /**
   * Verifica se ASIA è impostato su "SÌ"
   */
  isAsiaEnabled(): boolean {
    const asiaValue = this.quadroNeurologicoForm.get('asia')?.value;
    return this.datiCliniciService.convertBooleanValue(asiaValue) === true;
  }

  /**
   * Aggiorna lo stato dei campi punteggio in base ai livelli selezionati
   */
  updatePunteggioFieldsStatus() {
    const isAcutiDimissione = this.isAcutiDimissione();
    const livelloNeurologicoField = { livello: 'livelloNeurologico', punteggio: 'punteggioLivelloNeurologico' };
    const otherPunteggioFields = [
      { livello: 'livelloMotorioDx', punteggio: 'punteggioLivelloMotorioDx' },
      { livello: 'livelloMotorioSx', punteggio: 'punteggioLivelloMotorioSx' },
      { livello: 'livelloSensitivoDx', punteggio: 'punteggioLivelloSensitivoDx' },
      { livello: 'livelloSensitivoSx', punteggio: 'punteggioLivelloSensitivoSx' },
    ];

    const livelloNeurologicoControl = this.quadroNeurologicoForm.get(livelloNeurologicoField.livello);
    const punteggioNeurologicoControl = this.quadroNeurologicoForm.get(livelloNeurologicoField.punteggio);

    if (livelloNeurologicoControl?.status !== 'DISABLED' && livelloNeurologicoControl?.value) {
      punteggioNeurologicoControl?.enable();
      if (!isAcutiDimissione) {
        punteggioNeurologicoControl?.setValidators([Validators.required, Validators.pattern('^[0-9]{1,2}$')]);
      }
    } else {
      punteggioNeurologicoControl?.disable();
      punteggioNeurologicoControl?.clearValidators();
      punteggioNeurologicoControl?.setValue(null);
    }
    punteggioNeurologicoControl?.updateValueAndValidity();

    otherPunteggioFields.forEach(field => {
      const livelloControl = this.quadroNeurologicoForm.get(field.livello);
      const punteggioControl = this.quadroNeurologicoForm.get(field.punteggio);

      if (livelloControl?.status !== 'DISABLED' && livelloControl?.value) {
        punteggioControl?.enable();
        if (!isAcutiDimissione) {
          punteggioControl?.setValidators([Validators.required, Validators.pattern('^[0-9]{1,2}$')]);
        }
      } else {
        punteggioControl?.disable();
        punteggioControl?.clearValidators();
        punteggioControl?.setValue(null);
      }

      punteggioControl?.updateValueAndValidity();
    });
  }

  /**
   * Gestisce l'abilitazione/disabilitazione dei campi dipendenti da ASIA
   */
  updateAsiaFieldsStatus() {
    const isAsiaSi = this.isAsiaEnabled();
    const isAcutiDimissione = this.isAcutiDimissione();

    const asiaDependentFields = [
      'dataValutazioneAsia',
      'ais',
      'livelloNeurologico',
      'livelloMotorioDx',
      'livelloMotorioSx',
      'livelloSensitivoDx',
      'livelloSensitivoSx'
    ];

    let aisWasDisabled = false;
    let sensitivoDxWasDisabled = false;
    let sensitivoSxWasDisabled = false;

    asiaDependentFields.forEach(field => {
      const control = this.quadroNeurologicoForm.get(field);
      if (isAsiaSi) {
        control?.enable();

        if (!isAcutiDimissione && field === 'dataValutazioneAsia') {
          control?.addValidators([Validators.required, Validators.max(moment().valueOf())]);
        } else if (field === 'ais' || field === 'livelloNeurologico') {
          control?.setValidators([Validators.required]);
        }
      } else {
        control?.disable();
        if (field === 'dataValutazioneAsia') {
          control?.removeValidators(Validators.required);
        }
        if (control) {
          if (field === 'ais') {
            control.setValue('');
            aisWasDisabled = true;
          } else if (field === 'livelloSensitivoDx') {
            control.setValue(null);
            sensitivoDxWasDisabled = true;
          } else if (field === 'livelloSensitivoSx') {
            control.setValue(null);
            sensitivoSxWasDisabled = true;
          } else {
            control.setValue(null);
          }
        }
      }
      control?.updateValueAndValidity();
    });

    // Reset noteAis se ais è stato disabilitato
    if (aisWasDisabled) {
      this.datiCliniciService.resetNoteFields(this.quadroNeurologicoForm, ['noteAis']);
    }
    // Reset notePunteggi se entrambi i sensitivi sono stati disabilitati
    if (sensitivoDxWasDisabled && sensitivoSxWasDisabled) {
      this.datiCliniciService.resetNoteFields(this.quadroNeurologicoForm, ['notePunteggi']);
    }

    this.updatePunteggioFieldsStatus();
  }

  /**
   * Apre il popup per inserire note relative a un campo specifico
   */
  openPopupNote(campo: string) {
    let isDisabled = false;

    switch (campo) {
      case 'asia':
        isDisabled = this.quadroNeurologicoForm.get('asia')?.disabled || false;
        break;
      case 'ais':
        isDisabled = this.quadroNeurologicoForm.get('ais')?.disabled || false;
        break;
      case 'livelloSensitivo':
        isDisabled = (this.quadroNeurologicoForm.get('livelloSensitivoDx')?.disabled &&
          this.quadroNeurologicoForm.get('livelloSensitivoSx')?.disabled) || false;
        break;
    }


    let noteControl: AbstractControl | null = null;
    switch (campo) {
      case 'asia':
        noteControl = this.quadroNeurologicoForm.get('noteAsia');
        break;
      case 'ais':
        noteControl = this.quadroNeurologicoForm.get('noteAis');
        break;
      case 'livelloSensitivo':
        noteControl = this.quadroNeurologicoForm.get('notePunteggi');
        break;
    }

    if (noteControl) {
      let configNote = this.modalService.createNoteParam(noteControl.value, this.readOnly);

      this.modalService.note(configNote).subscribe(res => {
        if (res !== null && res !== noteControl?.value) {
          noteControl?.setValue(res);
          noteControl?.markAsDirty();
          noteControl?.updateValueAndValidity();
        }
      });

      this.datiCliniciService.adjustDialogHeight();
    }
  }

  /**
   * Popola il form con i dati del quadro neurologico
   */
  populateForm(val: SchedaQuadroNeurologicoModel | SchedaQuadroNeurologicoDimissioneModel) {
    // gestisco entrambe le casistiche (dimissioni/dati clinici)
    if (this.isAcutiDimissione()) {
      val = val as SchedaQuadroNeurologicoDimissioneModel;
      this.quadroNeurologicoForm.patchValue({
        dataValutazione: convertToDate(val.dataValutazioneNeurologica),
        asia: this.trovaDizionario(this.optionsAsia, val.asia),
        dataValutazioneAsia: convertToDate(val.dataValutazioneAsia),
        ais: val.ais || '',
        livelloNeurologico: this.trovaDizionario(this.optionsLivelliVertebre, val.livelloNeurologico),
        livelloMotorioDx: this.trovaDizionario(this.optionsLivelliVertebre, val.livelloMotorioDx),
        livelloMotorioSx: this.trovaDizionario(this.optionsLivelliVertebre, val.livelloMotorioSx),
        livelloSensitivoDx: this.trovaDizionario(this.optionsLivelliVertebre, val.livelloSensitivoDx),
        livelloSensitivoSx: this.trovaDizionario(this.optionsLivelliVertebre, val.livelloSensitivoSx),
        noteAsia: val.noteAsia || '',
        noteAis: val.noteAis || '',
        notePunteggi: val.notePunteggi || '',
      }, { emitEvent: false });
      this.quadroNeurologicoForm.removeControl('scim');
      this.quadroNeurologicoForm.removeControl('scimCuraSe');
      this.quadroNeurologicoForm.removeControl('scimRespirazione');
      this.quadroNeurologicoForm.removeControl('scimMobilita');
    } else {
      val = val as SchedaQuadroNeurologicoModel;
      this.quadroNeurologicoForm.patchValue({
        dataValutazione: convertToDate(val.dataValutazioneNeurologica),
        asia: this.trovaDizionario(this.optionsAsia, val.asia),
        dataValutazioneAsia: convertToDate(val.dataValutazioneAsia),
        ais: val.ais || '',
        livelloNeurologico: this.trovaDizionario(this.optionsLivelliVertebre, val.livelloNeurologico),
        livelloMotorioDx: this.trovaDizionario(this.optionsLivelliVertebre, val.livelloMotorioDx),
        livelloMotorioSx: this.trovaDizionario(this.optionsLivelliVertebre, val.livelloMotorioSx),
        livelloSensitivoDx: this.trovaDizionario(this.optionsLivelliVertebre, val.livelloSensitivoDx),
        livelloSensitivoSx: this.trovaDizionario(this.optionsLivelliVertebre, val.livelloSensitivoSx),
        noteAsia: val.noteAsia || '',
        noteAis: val.noteAis || '',
        notePunteggi: val.notePunteggi || '',
        scim: val.scim
      }, { emitEvent: false });

      if (val.scim) {
        this.validazioneScimAttiva = true
        this.quadroNeurologicoForm.get('scimCuraSe')?.enable();
        this.quadroNeurologicoForm.get('scimRespirazione')?.enable();
        this.quadroNeurologicoForm.get('scimMobilita')?.enable();

        this.quadroNeurologicoForm.get('scimCuraSe')?.setValue(val.scimCuraSe !== null ? val.scimCuraSe.toString() : '', { emitEvent: false });
        this.quadroNeurologicoForm.get('scimRespirazione')?.setValue(val.scimRespirazione !== null ? val.scimRespirazione.toString() : '', { emitEvent: false });
        this.quadroNeurologicoForm.get('scimMobilita')?.setValue(val.scimMobilita !== null ? val.scimMobilita.toString() : '', { emitEvent: false });
      }


    }

    if (val.punteggioLivelloNeurologico) {
      this.quadroNeurologicoForm.get('punteggioLivelloNeurologico')?.enable();
      this.quadroNeurologicoForm.get('punteggioLivelloNeurologico')?.setValue(val.punteggioLivelloNeurologico, { emitEvent: false });
    }

    if (val.punteggioLivelloMotorioDx) {
      this.quadroNeurologicoForm.get('punteggioLivelloMotorioDx')?.enable();
      this.quadroNeurologicoForm.get('punteggioLivelloMotorioDx')?.setValue(val.punteggioLivelloMotorioDx, { emitEvent: false });
    }

    if (val.punteggioLivelloMotorioSx) {
      this.quadroNeurologicoForm.get('punteggioLivelloMotorioSx')?.enable();
      this.quadroNeurologicoForm.get('punteggioLivelloMotorioSx')?.setValue(val.punteggioLivelloMotorioSx, { emitEvent: false });
    }

    if (val.punteggioLivelloSensitivoDx) {
      this.quadroNeurologicoForm.get('punteggioLivelloSensitivoDx')?.enable();
      this.quadroNeurologicoForm.get('punteggioLivelloSensitivoDx')?.setValue(val.punteggioLivelloSensitivoDx, { emitEvent: false });
    }

    if (val.punteggioLivelloSensitivoSx) {
      this.quadroNeurologicoForm.get('punteggioLivelloSensitivoSx')?.enable();
      this.quadroNeurologicoForm.get('punteggioLivelloSensitivoSx')?.setValue(val.punteggioLivelloSensitivoSx, { emitEvent: false });
    }
    this.disableInitialFields();
  }

  /**
   * Cerca un elemento del dizionario in base all'ID o all'oggetto
   */
  private trovaDizionario(options: Array<DizionarioModel>, value: any): DizionarioModel | null {
    if (!value) return null;

    if (typeof value === 'number') {
      return options.find(opt => opt.idDizionario === value) || null;
    }

    if (value.idDizionario) {
      return options.find(opt => opt.idDizionario === value.idDizionario) || null;
    }

    return null;
  }

  /**
   * Converte i valori del form in un modello per il salvataggio
   */
  mapFormToModel(formValue: any) {
    return {
      idScheda: this.datiQuadroNeurologico?.idScheda,
      nomeScheda: this.datiQuadroNeurologico?.nomeScheda || '',
      dataValutazioneNeurologica: formValue.dataValutazione ? moment(formValue.dataValutazione).format('YYYY-MM-DD') : undefined,
      asia: formValue.asia,
      dataValutazioneAsia: formValue.dataValutazioneAsia ? moment(formValue.dataValutazioneAsia).format('YYYY-MM-DD') : undefined,
      ais: formValue.ais,
      livelloNeurologico: formValue.livelloNeurologico,
      livelloMotorioDx: formValue.livelloMotorioDx,
      livelloMotorioSx: formValue.livelloMotorioSx,
      livelloSensitivoDx: formValue.livelloSensitivoDx,
      livelloSensitivoSx: formValue.livelloSensitivoSx,
      punteggioLivelloNeurologico: formValue.punteggioLivelloNeurologico,
      punteggioLivelloMotorioDx: formValue.punteggioLivelloMotorioDx,
      punteggioLivelloMotorioSx: formValue.punteggioLivelloMotorioSx,
      punteggioLivelloSensitivoDx: formValue.punteggioLivelloSensitivoDx,
      punteggioLivelloSensitivoSx: formValue.punteggioLivelloSensitivoSx,
      noteAsia: formValue.noteAsia,
      noteAis: formValue.noteAis,
      notePunteggi: formValue.notePunteggi,
      scim: formValue.scim ?? false,
      scimCuraSe: formValue.scimCuraSe ? parseInt(formValue.scimCuraSe) : 0,
      scimRespirazione: formValue.scimRespirazione ? parseInt(formValue.scimRespirazione) : 0,
      scimMobilita: formValue.scimMobilita ? parseInt(formValue.scimMobilita) : 0
    };
  }

  /**
   * Verifica se un controllo del form ha il validatore required
   */
  isRequired(controlName: string): boolean {
    const control = this.quadroNeurologicoForm.get(controlName);
    return this.datiCliniciService.isRequired(control);
  }

  /**
   * Limita l'input a massimo 2 cifre numeriche
   */
  restrictToTwoDigits(event: KeyboardEvent): boolean {
    return this.datiCliniciService.restrictToTwoDigits(event);
  }
}