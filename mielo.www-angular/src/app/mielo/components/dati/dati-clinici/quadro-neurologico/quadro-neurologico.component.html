<form [formGroup]="quadroNeurologicoForm" class="box-dati mt-4">
    <div>
        <!-- Data valutazione neurologica/fisiatrica -->
        <div class="row mb-3">
            <div class="col-12 col-md-4 mb-3">
                <div class="form-field-container">
                    <label class="field-label mb-2">Data valutazione neurologica/fisiatrica<span
                            *ngIf="!isAcutiDimissione()">*</span></label>
                    <mat-form-field appearance="outline">
                        <input matInput formControlName="dataValutazione" [max]="moment()" [matDatepicker]="picker"
                            placeholder="GG/MM/AAAA">
                        <mat-datepicker-toggle matSuffix [for]="picker">
                            <mat-icon svgIcon="it-calendar" matDatepickerToggleIcon></mat-icon>
                        </mat-datepicker-toggle>
                        <mat-datepicker #picker></mat-datepicker>
                        <mat-error *ngIf="quadroNeurologicoForm.get('dataValutazione')?.hasError('required')">
                            Campo obbligatorio
                        </mat-error>
                        <mat-error *ngIf="quadroNeurologicoForm.get('dataValutazione')?.hasError('matDatepickerMax')">
                            {{ERROR_MESSAGE.MAX_DATE}}
                        </mat-error>
                    </mat-form-field>
                </div>
            </div>
        </div>

        <!-- ASIA -->
        <div class="row my-3">
            <div class="col-12 col-md-6 col-lg-4 my-3 mr-1">
                <div class="form-field-container">
                    <label class="field-label mb-2">ASIA<span *ngIf="!isAcutiDimissione()">*</span></label>
                    <div class="d-flex align-items-start">
                        <div class="radio-group-container">
                            <mat-radio-group formControlName="asia" class="col-auto d-flex">
                                <ng-container *ngFor="let option of optionsAsia; let i = index">
                                    <mat-radio-button [value]="option" color="primary" [class.ms-4]="i !== 0"
                                        (mouseup)="datiCliniciService.toggleRadioSelection(quadroNeurologicoForm.get('asia'), option, $event)">
                                        {{  option?.descrizione == 'SI' ? 'Sì' : option?.descrizione | capitalizeFirst }}
                                    </mat-radio-button>
                                </ng-container>
                            </mat-radio-group>
                            <div class="col-auto">
                                <button mat-button type="button" class="note-button" (click)="openPopupNote('asia')"
                                    [disabled]="isNoteDisabled('asia')" style="background-color: transparent !important;">
                                    <svg class="icon"
                                        [ngClass]="{'icon-primary': !quadroNeurologicoForm.get('asia')?.disabled}">
                                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                    </svg>
                                    <span class="ms-1">Note</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Data valutazione ASIA -->
            <div class="col-12 col-md-4 my-3">
                <div class="form-field-container">
                    <label class="field-label mb-2">Data valutazione ASIA<span
                            *ngIf="isRequired('dataValutazioneAsia') && !isAcutiDimissione()">*</span></label>
                    <mat-form-field appearance="outline">
                        <input matInput formControlName="dataValutazioneAsia"
                               [max]="moment()"
                               [matDatepicker]="asiaPicker"
                               placeholder="GG/MM/AAAA">
                        <mat-datepicker-toggle matSuffix [for]="asiaPicker">
                            <mat-icon svgIcon="it-calendar" matDatepickerToggleIcon></mat-icon>
                        </mat-datepicker-toggle>
                        <mat-datepicker #asiaPicker></mat-datepicker>
                        <mat-error *ngIf="quadroNeurologicoForm.get('dataValutazioneAsia')?.hasError('required')">
                            {{ERROR_MESSAGE.REQUIRED}}
                        </mat-error>
                        <mat-error
                            *ngIf="quadroNeurologicoForm.get('dataValutazioneAsia')?.hasError('matDatepickerMax')">
                            {{ERROR_MESSAGE.MAX_DATE}}
                        </mat-error>
                    </mat-form-field>
                </div>
            </div>

        </div>

        <!-- AIS -->
        <div class="row my-3">
            <div class="col-12 col-md-6 mb-3">
                <div class="form-field-container">
                    <label class="field-label mb-2">AIS<span
                            *ngIf="isRequired('ais') && !isAcutiDimissione()">*</span></label>
                    <div class="d-flex align-items-start">
                        <div class="radio-group-container">
                            <mat-radio-group formControlName="ais" class="col-auto d-flex">
                                <mat-radio-button value="A" color="primary"
                                    (mouseup)="datiCliniciService.toggleRadioSelection(quadroNeurologicoForm.get('ais'), 'A', $event)">
                                    A
                                </mat-radio-button>
                                <mat-radio-button value="B" class="ms-4" color="primary"
                                    (mouseup)="datiCliniciService.toggleRadioSelection(quadroNeurologicoForm.get('ais'), 'B', $event)">
                                    B
                                </mat-radio-button>
                                <mat-radio-button value="C" class="ms-4" color="primary"
                                    (mouseup)="datiCliniciService.toggleRadioSelection(quadroNeurologicoForm.get('ais'), 'C', $event)">
                                    C
                                </mat-radio-button>
                                <mat-radio-button value="D" class="ms-4" color="primary"
                                    (mouseup)="datiCliniciService.toggleRadioSelection(quadroNeurologicoForm.get('ais'), 'D', $event)">
                                    D
                                </mat-radio-button>
                                <mat-radio-button value="E" class="ms-4" color="primary"
                                    (mouseup)="datiCliniciService.toggleRadioSelection(quadroNeurologicoForm.get('ais'), 'E', $event)">
                                    E
                                </mat-radio-button>
                            </mat-radio-group>
                            <div class="col-auto">
                                <button mat-button type="button" class="note-button" (click)="openPopupNote('ais')"
                                    [disabled]="isNoteDisabled('ais')" style="background-color: transparent !important;">
                                    <svg class="icon" [ngClass]="{'icon-primary': !quadroNeurologicoForm.get('ais')?.disabled}">
                                        <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                    </svg>
                                    <span class="ms-1">Note</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="d-flex flex-column">
            <div class="d-flex">
                <!-- Livelli neurologici -->
                <div class="row mb-3 col-4">
                    <!-- Livello Neurologico -->
                    <div class="col-7 col-md-7 mb-3">
                        <div class="form-field-container">
                            <label class="field-label mb-2">Livello Neurologico<span
                                    *ngIf="isRequired('livelloNeurologico') && !isAcutiDimissione()">*</span></label>
                            <mat-form-field appearance="outline" class="w-100">
                                <mat-select formControlName="livelloNeurologico" placeholder="Seleziona">
                                    <mat-option [value]="null"></mat-option>
                                    <mat-option *ngFor="let option of optionsLivelliVertebre" [value]="option">
                                        {{ option.descrizione }}
                                    </mat-option>
                                </mat-select>
                                <mat-error
                                    *ngIf="quadroNeurologicoForm.get('livelloNeurologico')?.hasError('required')">
                                    Campo obbligatorio
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>

                    <!-- Punteggio Livello Neurologico -->
                    <div class="col-5 col-md-5 mb-3 align-content-end">
                        <div class="form-field-container">
                            <mat-form-field appearance="outline" class="w-100">
                                <input matInput type="number" formControlName="punteggioLivelloNeurologico"
                                    [placeholder]="'Inserisci' + (quadroNeurologicoForm.get('livelloNeurologico')?.value ? '*' : '')"
                                    maxlength="2" min="0" max="99" pattern="[0-9]{1,2}"
                                    (keydown)="restrictToTwoDigits($event)">
                                <mat-error
                                    *ngIf="quadroNeurologicoForm.get('punteggioLivelloNeurologico')?.hasError('required')">
                                    Campo obbligatorio
                                </mat-error>
                                <mat-error
                                    *ngIf="quadroNeurologicoForm.get('punteggioLivelloNeurologico')?.hasError('pattern')">
                                    Inserire un numero di massimo 2 cifre
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                </div>

                <!-- Livelli motori -->
                <div class="row mb-3 px-3 col-4">
                    <!-- Livello motorio DX -->
                    <div class="col-7 col-md-7 mb-3">
                        <div class="form-field-container">
                            <label class="field-label mb-2">Livello motorio DX</label>
                            <mat-form-field appearance="outline" class="w-100">
                                <mat-select formControlName="livelloMotorioDx" placeholder="Seleziona">
                                    <mat-option [value]="null"></mat-option>
                                    <mat-option *ngFor="let option of optionsLivelliVertebre" [value]="option">
                                        {{ option.descrizione }}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>

                    <!-- Punteggio Livello motorio DX -->
                    <div class="col-5 col-md-5 mb-3 align-content-end">
                        <div class="form-field-container">
                            <mat-form-field appearance="outline" class="w-100">
                                <input matInput type="number" formControlName="punteggioLivelloMotorioDx"
                                    [placeholder]="'Inserisci' + (quadroNeurologicoForm.get('livelloMotorioDx')?.value ? '*' : '')"
                                    maxlength="2" min="0" max="99" pattern="[0-9]{1,2}"
                                    (keydown)="restrictToTwoDigits($event)">
                                <mat-error
                                    *ngIf="quadroNeurologicoForm.get('punteggioLivelloMotorioDx')?.hasError('required')">
                                    Campo obbligatorio
                                </mat-error>
                                <mat-error
                                    *ngIf="quadroNeurologicoForm.get('punteggioLivelloMotorioDx')?.hasError('pattern')">
                                    Inserire un numero di massimo 2 cifre
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                </div>

                <div class="row mb-3 col-4">
                    <!-- Livello motorio SX -->
                    <div class="col-7 col-md-7 mb-3">
                        <div class="form-field-container">
                            <label class="field-label mb-2">Livello motorio SX</label>
                            <mat-form-field appearance="outline" class="w-100">
                                <mat-select formControlName="livelloMotorioSx" placeholder="Seleziona">
                                    <mat-option [value]="null"></mat-option>
                                    <mat-option *ngFor="let option of optionsLivelliVertebre" [value]="option">
                                        {{ option.descrizione }}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>

                    <!-- Punteggio Livello motorio SX -->
                    <div class="col-5 col-md-5 mb-3 align-content-end">
                        <div class="form-field-container">
                            <mat-form-field appearance="outline" class="w-100">
                                <input matInput type="number" formControlName="punteggioLivelloMotorioSx"
                                    [placeholder]="'Inserisci' + (quadroNeurologicoForm.get('livelloMotorioSx')?.value ? '*' : '')"
                                    maxlength="2" min="0" max="99" pattern="[0-9]{1,2}"
                                    (keydown)="restrictToTwoDigits($event)">
                                <mat-error
                                    *ngIf="quadroNeurologicoForm.get('punteggioLivelloMotorioSx')?.hasError('required')">
                                    Campo obbligatorio
                                </mat-error>
                                <mat-error
                                    *ngIf="quadroNeurologicoForm.get('punteggioLivelloMotorioSx')?.hasError('pattern')">
                                    Inserire un numero di massimo 2 cifre
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                </div>
            </div>
            <div class="d-flex">
                <!-- Livelli sensitivi -->
                <div class="row mb-3 col-4">
                    <!-- Livello sensitivo DX -->
                    <div class="col-7 col-md-7 mb-3">
                        <div class="form-field-container">
                            <label class="field-label mb-2">Livello sensitivo DX</label>
                            <mat-form-field appearance="outline" class="w-100">
                                <mat-select formControlName="livelloSensitivoDx" placeholder="Seleziona">
                                    <mat-option [value]="null"></mat-option>
                                    <mat-option *ngFor="let option of optionsLivelliVertebre" [value]="option">
                                        {{ option.descrizione }}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>

                    <!-- Punteggio Livello sensitivo DX -->
                    <div class="col-5 col-md-5 mb-3 align-content-end">
                        <div class="form-field-container">
                            <mat-form-field appearance="outline" class="w-100">
                                <input matInput type="number" formControlName="punteggioLivelloSensitivoDx"
                                    [placeholder]="'Inserisci' + (quadroNeurologicoForm.get('livelloSensitivoDx')?.value ? '*' : '')"
                                    maxlength="2" min="0" max="99" pattern="[0-9]{1,2}"
                                    (keydown)="restrictToTwoDigits($event)">
                                <mat-error
                                    *ngIf="quadroNeurologicoForm.get('punteggioLivelloSensitivoDx')?.hasError('required')">
                                    Campo obbligatorio
                                </mat-error>
                                <mat-error
                                    *ngIf="quadroNeurologicoForm.get('punteggioLivelloSensitivoDx')?.hasError('pattern')">
                                    Inserire un numero di massimo 2 cifre
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                </div>

                <div class="row mb-3 col-4 px-3">
                    <!-- Livello sensitivo SX -->
                    <div class="col-7 col-md-7 mb-3">
                        <div class="form-field-container">
                            <label class="field-label mb-2">Livello sensitivo SX</label>
                            <mat-form-field appearance="outline" class="w-100">
                                <mat-select formControlName="livelloSensitivoSx" placeholder="Seleziona">
                                    <mat-option [value]="null"></mat-option>
                                    <mat-option *ngFor="let option of optionsLivelliVertebre" [value]="option">
                                        {{ option.descrizione }}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>

                    <!-- Punteggio Livello sensitivo SX -->
                    <div class="col-5 col-md-5 mb-3 align-content-end">
                        <div class="form-field-container">
                            <mat-form-field appearance="outline" class="w-100">
                                <input matInput type="number" formControlName="punteggioLivelloSensitivoSx"
                                    [placeholder]="'Inserisci' + (quadroNeurologicoForm.get('livelloSensitivoSx')?.value ? '*' : '')"
                                    maxlength="2" min="0" max="99" pattern="[0-9]{1,2}"
                                    (keydown)="restrictToTwoDigits($event)">
                                <mat-error
                                    *ngIf="quadroNeurologicoForm.get('punteggioLivelloSensitivoSx')?.hasError('required')">
                                    Campo obbligatorio
                                </mat-error>
                                <mat-error
                                    *ngIf="quadroNeurologicoForm.get('punteggioLivelloSensitivoSx')?.hasError('pattern')">
                                    Inserire un numero di massimo 2 cifre
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                </div>

                <div class="pl-0 col-12 col-md-4 mb-3">
                    <div class="pl-0 col-7 col-md-7 mb-3">
                        <div class=" pl-0 mt-4 pt-2">
                            <button mat-button type="button" class="note-button"
                                (click)="openPopupNote('livelloSensitivo')" [disabled]="!isAsiaEnabled()"
                                style="background-color: transparent !important;">
                                <svg class="icon"
                                    [ngClass]="{'icon-primary': !(quadroNeurologicoForm.get('livelloSensitivoSx')?.disabled &&
                                                                                quadroNeurologicoForm.get('livelloSensitivoSx')?.disabled)}">
                                    <use xlink:href="./assets/bootstrap-lombardia/svg/sprite.svg#it-note"></use>
                                </svg>
                                <span class="ms-1">Note</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>


        </div>

        <!-- SCIM -->
        <ng-container *ngIf="!isDimissione">
            <hr class="my-4">
            <div class="row mb-3">
                <div class="col-12 col-md-4 mb-3">
                    <div class="form-field-container">
                        <label class="field-label mb-2">SCIM<span *ngIf="isRequired('scim')">*</span></label>
                        <div class="radio-group-container">
                            <mat-radio-group formControlName="scim" class="d-flex">
                                <mat-radio-button [value]="true" color="primary"
                                    (mouseup)="datiCliniciService.toggleRadioSelection(quadroNeurologicoForm.get('scim'), true, $event)">
                                    Sì
                                </mat-radio-button>
                                <mat-radio-button [value]="false" class="ms-4" color="primary"
                                    (mouseup)="datiCliniciService.toggleRadioSelection(quadroNeurologicoForm.get('scim'), false, $event)">
                                    No
                                </mat-radio-button>
                            </mat-radio-group>
                        </div>
                    </div>
                </div>
            </div>

            <div>
                <div class="row mb-3">
                    <!-- SCIM – cura di sé -->
                    <div class="col-12 col-md-4 mb-3">
                        <div class="form-field-container">
                            <label class="field-label mb-2">SCIM – cura di sé<span
                                    *ngIf="validazioneScimAttiva">*</span></label>
                            <mat-form-field appearance="outline" class="w-100">
                                <input matInput formControlName="scimCuraSe" placeholder="Inserisci" type="number"
                                    (keydown)="restrictToTwoDigits($event)">
                                <mat-error *ngIf="quadroNeurologicoForm.get('scimCuraSe')?.hasError('required')">
                                    Campo obbligatorio
                                </mat-error>
                                <mat-error
                                    *ngIf="quadroNeurologicoForm.get('scimCuraSe')?.hasError('min') || quadroNeurologicoForm.get('scimCuraSe')?.hasError('max')">
                                    Valore ammesso da 0 a 20
                                </mat-error>
                                <mat-error *ngIf="quadroNeurologicoForm.get('scimCuraSe')?.hasError('pattern')">
                                    Inserire solo numeri
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>

                    <!-- SCIM – Respirazione/gestione sfinteri -->
                    <div class="col-12 col-md-4 mb-3">
                        <div class="form-field-container">
                            <label class="field-label mb-2">SCIM – Respirazione/gestione sfinteri<span
                                    *ngIf="validazioneScimAttiva">*</span></label>
                            <mat-form-field appearance="outline" class="w-100">
                                <input matInput formControlName="scimRespirazione" placeholder="Inserisci" type="number"
                                    (keydown)="restrictToTwoDigits($event)">
                                <mat-error *ngIf="quadroNeurologicoForm.get('scimRespirazione')?.hasError('required')">
                                    Campo obbligatorio
                                </mat-error>
                                <mat-error
                                    *ngIf="quadroNeurologicoForm.get('scimRespirazione')?.hasError('min') || quadroNeurologicoForm.get('scimRespirazione')?.hasError('max')">
                                    Valore ammesso da 0 a 40
                                </mat-error>
                                <mat-error *ngIf="quadroNeurologicoForm.get('scimRespirazione')?.hasError('pattern')">
                                    Inserire solo numeri
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>

                    <!-- SCIM – Mobilità -->
                    <div class="col-12 col-md-4 mb-3">
                        <div class="form-field-container">
                            <label class="field-label mb-2">SCIM – Mobilità<span
                                    *ngIf="validazioneScimAttiva">*</span></label>
                            <mat-form-field appearance="outline" class="w-100">
                                <input matInput formControlName="scimMobilita" placeholder="Inserisci" type="number"
                                    (keydown)="restrictToTwoDigits($event)">
                                <mat-error *ngIf="quadroNeurologicoForm.get('scimMobilita')?.hasError('required')">
                                    Campo obbligatorio
                                </mat-error>
                                <mat-error
                                    *ngIf="quadroNeurologicoForm.get('scimMobilita')?.hasError('min') || quadroNeurologicoForm.get('scimMobilita')?.hasError('max')">
                                    Valore ammesso da 0 a 40
                                </mat-error>
                                <mat-error *ngIf="quadroNeurologicoForm.get('scimMobilita')?.hasError('pattern')">
                                    Inserire solo numeri
                                </mat-error>
                            </mat-form-field>
                        </div>
                    </div>
                </div>
            </div>

        </ng-container>

    </div>
</form>