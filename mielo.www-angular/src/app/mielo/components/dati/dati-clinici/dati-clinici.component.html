<div class="container-data pt-4">

  <h3 class="ml-3 mb-0">Dati clinici</h3>

  <!-- Eziologia -->
  <mat-accordion *ngIf="datiClinici?.schedaEziologica" class="m-2">
    <mat-expansion-panel (opened)="onOpened('eziologia')" [expanded]="openEziologia">
      <mat-expansion-panel-header>
        <mat-panel-title>
          <span class="panel-title">EZIOLOGIA</span>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <ng-container *ngIf="hasBeenOpenedEziologia">
        <app-eziologia [readOnly]="eziologiaReadOnly" [idReparto]="idReparto" (formInitialized)="onEziologiaReady()">
        </app-eziologia>
      </ng-container>
    </mat-expansion-panel>
  </mat-accordion>

  <!-- Lesione e trattamento -->
  <mat-accordion *ngIf="datiClinici?.schedaLesioneTrattamento" class="m-2">
    <mat-expansion-panel (opened)="onOpened('lesione')">
      <mat-expansion-panel-header>
        <mat-panel-title>
          <span class="panel-title">LESIONE E TRATTAMENTO</span>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <ng-container *ngIf="hasBeenOpenedLesione">
        <app-lesione-trattamento [readOnly]="lesioneReadOnly" [idReparto]="idReparto">
        </app-lesione-trattamento>
      </ng-container>
    </mat-expansion-panel>
  </mat-accordion>

  <!-- Valutazione in ingresso -->
  <mat-accordion *ngIf="datiClinici?.schedaValutazioneIngresso" class="m-2">
    <mat-expansion-panel (opened)="onOpened('valutazione')">
      <mat-expansion-panel-header>
        <mat-panel-title class="d-flex justify-content-between">
          <span class="panel-title">VALUTAZIONE IN INGRESSO <span *ngIf="tipoEvento !== TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO.idTipoEvento">(ENTRO UNA SETTIMANA DALL'INGRESSO)</span></span>
            <div *ngIf="tipoEvento === TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO.idTipoEvento"
                 class="d-flex position-relative">
                <button mat-button type="button" class="p-0 cronologia-button"
                        (click)="getCronologia(ETipoScheda.VALUTAZIONE_IN_INGRESSO, $event)">
                  Cronologia
                </button>

                <!-- Menu custom cronologia -->
                <div *ngIf="isMenuOpen(ETipoScheda.VALUTAZIONE_IN_INGRESSO)" class="cronologia-menu">
                  <div class="cronologia-menu-header">
                    <h4>{{ getMenuTitle(ETipoScheda.VALUTAZIONE_IN_INGRESSO) }}</h4>
                    <button mat-icon-button (click)="closeCronologiaMenu(getMenuKey(ETipoScheda.VALUTAZIONE_IN_INGRESSO))" class="close-button">
                      <span>&times;</span>
                    </button>
                  </div>

                  <div class="cronologia-menu-content">
                    <div *ngIf="isMenuLoading(ETipoScheda.VALUTAZIONE_IN_INGRESSO)" class="loading-container">
                      <div class="spinner"></div>
                      <span>Caricamento...</span>
                    </div>

                    <div *ngIf="!isMenuLoading(ETipoScheda.VALUTAZIONE_IN_INGRESSO) && getMenuData(ETipoScheda.VALUTAZIONE_IN_INGRESSO).length === 0" class="no-data">
                      Nessun dato disponibile
                    </div>

                    <div *ngIf="!isMenuLoading(ETipoScheda.VALUTAZIONE_IN_INGRESSO) && getMenuData(ETipoScheda.VALUTAZIONE_IN_INGRESSO).length > 0" class="cronologia-items">
                      <div *ngFor="let item of getMenuData(ETipoScheda.VALUTAZIONE_IN_INGRESSO)" class="cronologia-item">
                        <div class="cronologia-item-header">
                          <strong>{{ item.medicoCompilatore }}</strong>
                          <span class="date">{{ item.dataUltimaModifica | date:'dd/MM/yyyy HH:mm' }}</span>
                        </div>
                        <div class="cronologia-item-body">
                          <div class="presidio">
                            <strong>Presidio:</strong> {{ item.presidioOspedaliero }}
                          </div>
                          <div class="tipo-scheda">
                            <strong>Tipo Scheda:</strong> {{ item.idTipoScheda }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
            </div>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <ng-container *ngIf="hasBeenOpenedValutazione">
        <app-valutazione
                [isDimissione]="false"
                [readOnly]="valutazioneReadOnly"
                [dataRicovero]="dataRicovero">
        </app-valutazione>
      </ng-container>
    </mat-expansion-panel>
  </mat-accordion>

  <!-- Necessità assistenziale in ingresso -->
  <mat-accordion *ngIf="datiClinici?.schedaNecessitaAssistenzialeIngresso" class="m-2">
    <mat-expansion-panel (opened)="onOpened('necessita')">
      <mat-expansion-panel-header>
        <mat-panel-title class="d-flex justify-content-between">
          <span class="panel-title">NECESSITÀ ASSISTENZIALI IN INGRESSO</span>
            <div *ngIf="tipoEvento === TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO.idTipoEvento"
                 class="d-flex position-relative">
                <button mat-button type="button" class="p-0 cronologia-button"
                        (click)="getCronologia(ETipoScheda.NECESSITA_ASSISTENZIALI_IN_INGRESSO, $event)">
                  Cronologia
                </button>

                <!-- Menu custom cronologia -->
                <div *ngIf="isMenuOpen(ETipoScheda.NECESSITA_ASSISTENZIALI_IN_INGRESSO)" class="cronologia-menu">
                  <div class="cronologia-menu-header">
                    <h4>{{ getMenuTitle(ETipoScheda.NECESSITA_ASSISTENZIALI_IN_INGRESSO) }}</h4>
                    <button mat-icon-button (click)="closeCronologiaMenu(getMenuKey(ETipoScheda.NECESSITA_ASSISTENZIALI_IN_INGRESSO))" class="close-button">
                      <span>&times;</span>
                    </button>
                  </div>

                  <div class="cronologia-menu-content">
                    <div *ngIf="isMenuLoading(ETipoScheda.NECESSITA_ASSISTENZIALI_IN_INGRESSO)" class="loading-container">
                      <div class="spinner"></div>
                      <span>Caricamento...</span>
                    </div>

                    <div *ngIf="!isMenuLoading(ETipoScheda.NECESSITA_ASSISTENZIALI_IN_INGRESSO) && getMenuData(ETipoScheda.NECESSITA_ASSISTENZIALI_IN_INGRESSO).length === 0" class="no-data">
                      Nessun dato disponibile
                    </div>

                    <div *ngIf="!isMenuLoading(ETipoScheda.NECESSITA_ASSISTENZIALI_IN_INGRESSO) && getMenuData(ETipoScheda.NECESSITA_ASSISTENZIALI_IN_INGRESSO).length > 0" class="cronologia-items">
                      <div *ngFor="let item of getMenuData(ETipoScheda.NECESSITA_ASSISTENZIALI_IN_INGRESSO)" class="cronologia-item">
                        <div class="cronologia-item-header">
                          <strong>{{ item.medicoCompilatore }}</strong>
                          <span class="date">{{ item.dataUltimaModifica | date:'dd/MM/yyyy HH:mm' }}</span>
                        </div>
                        <div class="cronologia-item-body">
                          <div class="presidio">
                            <strong>Presidio:</strong> {{ item.presidioOspedaliero }}
                          </div>
                          <div class="tipo-scheda">
                            <strong>Tipo Scheda:</strong> {{ item.idTipoScheda }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
            </div>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <ng-container *ngIf="hasBeenOpenedNecessita">
        <app-necessita-assistenziali
                [repartoType]="idReparto"
                [readOnly]="necessitaReadOnly"
                [isDimissione]="false"
        ></app-necessita-assistenziali>
      </ng-container>
    </mat-expansion-panel>
  </mat-accordion>

  <!-- Quadro neurologico -->
  <mat-accordion *ngIf="datiClinici?.schedaQuadroNeurologico" class="m-2">
    <mat-expansion-panel (opened)="onOpened('quadro')">
      <mat-expansion-panel-header>
        <mat-panel-title class="d-flex justify-content-between">
          <span class="panel-title">QUADRO NEUROLOGICO</span>
            <div *ngIf="tipoEvento === TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO.idTipoEvento"
                 class="d-flex position-relative">
                <button mat-button type="button" class="p-0 cronologia-button"
                        (click)="getCronologia(ETipoScheda.QUADRO_NEUROLOGICO, $event)">
                  Cronologia
                </button>

                <!-- Menu custom cronologia -->
                <div *ngIf="cronologiaMenuOpen" class="cronologia-menu">
                  <div class="cronologia-menu-header">
                    <h4>Cronologia Quadro Neurologico</h4>
                    <button mat-icon-button (click)="closeCronologiaMenu()" class="close-button">
                      <span>&times;</span>
                    </button>
                  </div>

                  <div class="cronologia-menu-content">
                    <div *ngIf="cronologiaLoading" class="loading-container">
                      <div class="spinner"></div>
                      <span>Caricamento...</span>
                    </div>

                    <div *ngIf="!cronologiaLoading && cronologiaData.length === 0" class="no-data">
                      Nessun dato disponibile
                    </div>

                    <div *ngIf="!cronologiaLoading && cronologiaData.length > 0" class="cronologia-items">
                      <div *ngFor="let item of cronologiaData" class="cronologia-item">
                        <div class="cronologia-item-header">
                          <strong>{{ item.medicoCompilatore }}</strong>
                          <span class="date">{{ item.dataUltimaModifica | date:'dd/MM/yyyy HH:mm' }}</span>
                        </div>
                        <div class="cronologia-item-body">
                          <div class="presidio">
                            <strong>Presidio:</strong> {{ item.presidioOspedaliero }}
                          </div>
                          <div class="tipo-scheda">
                            <strong>Tipo Scheda:</strong> {{ item.idTipoScheda }}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
            </div>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <ng-container *ngIf="hasBeenOpenedQuadro">
        <app-quadro-neurologico [readOnly]="quadroReadOnly" [isDimissione]="false"></app-quadro-neurologico>
      </ng-container>
    </mat-expansion-panel>
  </mat-accordion>

  <!-- Setting riabilitativo -->
  <mat-accordion *ngIf="datiClinici?.schedaSettingRiabilitativo" class="m-2">
    <mat-expansion-panel (opened)="onOpened('setting')">
      <mat-expansion-panel-header>
        <mat-panel-title>
          <span class="panel-title">SETTING RIABILITATIVO</span>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <ng-container *ngIf="hasBeenOpenedSetting">
        <app-setting-riabilitativo [readOnly]="settingReadOnly" [idReparto]="idReparto"
          (showDimissione)="onShowDimissione($event)">
        </app-setting-riabilitativo>
      </ng-container>
    </mat-expansion-panel>
  </mat-accordion>

  <!-- Complicanze -->
  <mat-accordion *ngIf="datiClinici?.schedaComplicanze" class="m-2">
    <mat-expansion-panel (opened)="onOpened('complicanze')">
      <mat-expansion-panel-header>
        <mat-panel-title>
          <span class="panel-title">COMPLICANZE</span>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <ng-container *ngIf="hasBeenOpenedComplicanze">
        <app-complicanze [readOnly]="complicanzeReadOnly"/>
      </ng-container>
    </mat-expansion-panel>
  </mat-accordion>
</div>
