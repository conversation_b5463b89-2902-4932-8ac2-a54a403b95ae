<div class="container-data pt-4">

  <h3 class="ml-3 mb-0">Dati clinici</h3>

  <!-- Eziologia -->
  <mat-accordion *ngIf="datiClinici?.schedaEziologica" class="m-2">
    <mat-expansion-panel (opened)="onOpened('eziologia')" [expanded]="openEziologia">
      <mat-expansion-panel-header>
        <mat-panel-title>
          <span class="panel-title">EZIOLOGIA</span>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <ng-container *ngIf="hasBeenOpenedEziologia">
        <app-eziologia [readOnly]="eziologiaReadOnly" [idReparto]="idReparto" (formInitialized)="onEziologiaReady()">
        </app-eziologia>
      </ng-container>
    </mat-expansion-panel>
  </mat-accordion>

  <!-- Lesione e trattamento -->
  <mat-accordion *ngIf="datiClinici?.schedaLesioneTrattamento" class="m-2">
    <mat-expansion-panel (opened)="onOpened('lesione')">
      <mat-expansion-panel-header>
        <mat-panel-title>
          <span class="panel-title">LESIONE E TRATTAMENTO</span>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <ng-container *ngIf="hasBeenOpenedLesione">
        <app-lesione-trattamento [readOnly]="lesioneReadOnly" [idReparto]="idReparto">
        </app-lesione-trattamento>
      </ng-container>
    </mat-expansion-panel>
  </mat-accordion>

  <!-- Valutazione in ingresso -->
  <mat-accordion *ngIf="datiClinici?.schedaValutazioneIngresso" class="m-2">
    <mat-expansion-panel (opened)="onOpened('valutazione')">
      <mat-expansion-panel-header>
        <mat-panel-title class="d-flex justify-content-between">
          <span class="panel-title">VALUTAZIONE IN INGRESSO <span *ngIf="tipoEvento !== TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO.idTipoEvento">(ENTRO UNA SETTIMANA DALL'INGRESSO)</span></span>
            <div *ngIf="tipoEvento === TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO.idTipoEvento"
                 class="d-flex">
                <button
                        matSuffix
                        type="button"
                        (click)="datiCronologia = null"
                        mat-icon-button>
                    <mat-icon>edit</mat-icon>
                    Torna all'editor
                </button>
                <app-cronologia-menu-simple
                  [onInfoClick]="onCronologiaDatiInfoClick"
                  [idTipoScheda]="ETipoScheda.VALUTAZIONE_IN_INGRESSO"
                  [idPaziente]="idPaziente">
                </app-cronologia-menu-simple>
            </div>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <ng-container *ngIf="hasBeenOpenedValutazione">
        <app-valutazione
                [cronologiaInfo]="datiCronologia"
                [isDimissione]="false"
                [readOnly]="valutazioneReadOnly"
                [dataRicovero]="dataRicovero">
        </app-valutazione>
      </ng-container>
    </mat-expansion-panel>
  </mat-accordion>

  <!-- Necessità assistenziale in ingresso -->
  <mat-accordion *ngIf="datiClinici?.schedaNecessitaAssistenzialeIngresso" class="m-2">
    <mat-expansion-panel (opened)="onOpened('necessita')">
      <mat-expansion-panel-header>
        <mat-panel-title class="d-flex justify-content-between">
          <span class="panel-title">NECESSITÀ ASSISTENZIALI IN INGRESSO</span>
            <div *ngIf="tipoEvento === TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO.idTipoEvento"
                 class="d-flex">
                <app-cronologia-menu-simple
                  [idTipoScheda]="ETipoScheda.NECESSITA_ASSISTENZIALI_IN_INGRESSO"
                  [idPaziente]="idPaziente">
                </app-cronologia-menu-simple>
            </div>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <ng-container *ngIf="hasBeenOpenedNecessita">
        <app-necessita-assistenziali
                [repartoType]="idReparto"
                [readOnly]="necessitaReadOnly"
                [isDimissione]="false"
        ></app-necessita-assistenziali>
      </ng-container>
    </mat-expansion-panel>
  </mat-accordion>

  <!-- Quadro neurologico -->
  <mat-accordion *ngIf="datiClinici?.schedaQuadroNeurologico" class="m-2">
    <mat-expansion-panel (opened)="onOpened('quadro')">
      <mat-expansion-panel-header>
        <mat-panel-title class="d-flex justify-content-between">
          <span class="panel-title">QUADRO NEUROLOGICO</span>
            <div *ngIf="tipoEvento === TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO.idTipoEvento"
                 class="d-flex">
                <app-cronologia-menu-simple
                  [idTipoScheda]="ETipoScheda.QUADRO_NEUROLOGICO"
                  [idPaziente]="idPaziente">
                </app-cronologia-menu-simple>
            </div>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <ng-container *ngIf="hasBeenOpenedQuadro">
        <app-quadro-neurologico [readOnly]="quadroReadOnly" [isDimissione]="false"></app-quadro-neurologico>
      </ng-container>
    </mat-expansion-panel>
  </mat-accordion>

  <!-- Setting riabilitativo -->
  <mat-accordion *ngIf="datiClinici?.schedaSettingRiabilitativo" class="m-2">
    <mat-expansion-panel (opened)="onOpened('setting')">
      <mat-expansion-panel-header>
        <mat-panel-title>
          <span class="panel-title">SETTING RIABILITATIVO</span>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <ng-container *ngIf="hasBeenOpenedSetting">
        <app-setting-riabilitativo [readOnly]="settingReadOnly" [idReparto]="idReparto"
          (showDimissione)="onShowDimissione($event)">
        </app-setting-riabilitativo>
      </ng-container>
    </mat-expansion-panel>
  </mat-accordion>

  <!-- Complicanze -->
  <mat-accordion *ngIf="datiClinici?.schedaComplicanze" class="m-2">
    <mat-expansion-panel (opened)="onOpened('complicanze')">
      <mat-expansion-panel-header>
        <mat-panel-title>
          <span class="panel-title">COMPLICANZE</span>
        </mat-panel-title>
      </mat-expansion-panel-header>
      <ng-container *ngIf="hasBeenOpenedComplicanze">
        <app-complicanze [readOnly]="complicanzeReadOnly"/>
      </ng-container>
    </mat-expansion-panel>
  </mat-accordion>
</div>


