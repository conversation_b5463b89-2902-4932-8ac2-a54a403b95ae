:host {
    .panel-title {
        color: #003354 !important;
        font-size: 24px;
        font-weight: 600;
    }

    ::ng-deep {
        .mat-expansion-panel {
            background-color: #ffffff;
            box-shadow: none;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            margin-bottom: 1rem;
        }

        .mat-expansion-panel-header {
            padding: 0 1.5rem;
            height: 48px;
        }
    }

    /* Cronologia Menu Styles */
    .position-relative {
        position: relative;
    }

    .cronologia-button {
        cursor: pointer;
        color: #0066cc;

        &:hover {
            background-color: rgba(0, 102, 204, 0.1);
        }
    }

    .cronologia-menu {
        position: absolute;
        top: 100%;
        right: 0;
        z-index: 1000;
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        min-width: 400px;
        max-width: 500px;
        max-height: 400px;
        overflow: hidden;

        .cronologia-menu-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #ddd;

            h4 {
                margin: 0;
                font-size: 14px;
                font-weight: 600;
                color: #333;
            }

            .close-button {
                width: 24px;
                height: 24px;
                min-width: 24px;
                padding: 0;
                line-height: 1;

                span {
                    font-size: 18px;
                    color: #666;
                }

                &:hover span {
                    color: #333;
                }
            }
        }

        .cronologia-menu-content {
            max-height: 320px;
            overflow-y: auto;

            .loading-container {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
                gap: 8px;

                .spinner {
                    width: 16px;
                    height: 16px;
                    border: 2px solid #f3f3f3;
                    border-top: 2px solid #0066cc;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }

                span {
                    font-size: 14px;
                    color: #666;
                }
            }

            .no-data {
                padding: 20px;
                text-align: center;
                color: #666;
                font-size: 14px;
            }

            .cronologia-items {
                .cronologia-item {
                    padding: 12px 16px;
                    border-bottom: 1px solid #eee;

                    &:last-child {
                        border-bottom: none;
                    }

                    &:hover {
                        background-color: #f8f9fa;
                    }

                    .cronologia-item-header {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 8px;

                        strong {
                            font-size: 14px;
                            color: #333;
                        }

                        .date {
                            font-size: 12px;
                            color: #666;
                        }
                    }

                    .cronologia-item-body {
                        font-size: 13px;

                        .presidio, .tipo-scheda {
                            margin-bottom: 4px;

                            strong {
                                color: #555;
                                margin-right: 4px;
                            }
                        }
                    }
                }
            }
        }
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .cronologia-menu {
            min-width: 300px;
            max-width: 90vw;
            right: -50px;
        }
    }
}