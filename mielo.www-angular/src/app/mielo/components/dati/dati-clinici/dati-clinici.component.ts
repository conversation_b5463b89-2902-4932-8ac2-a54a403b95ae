import { CommonModule } from '@angular/common';
import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, QueryList, ViewChildren } from '@angular/core';
import { MatExpansionModule, MatExpansionPanel } from '@angular/material/expansion';
import {merge, Subject, Subscription, takeUntil, tap} from 'rxjs';
import { OperatoreService } from '../../../../core/services/operatore.service';
import { SceltaSchedaEnum } from '../../../../shared/enums/scelta-scheda.enum';
import { SchedaDatiCliniciModel } from '../../../../shared/interfaces/dati-clinici.interface';
import { SchedaRicoveroDatiMancanti, SchedaRicoveroModel } from "../../../../shared/interfaces/scheda-ricovero.interface";
import { SchedaRicoveroService } from '../../../services/scheda-ricovero.service';
import { EziologiaComponent } from './eziologia/eziologia.component';
import { LesioneTrattamentoComponent } from './lesione-trattamento/lesione-trattamento.component';
import { NecessitaAssistenzialiComponent } from './necessita-assistenziali/necessita-assistenziali.component';
import { QuadroNeurologicoComponent } from './quadro-neurologico/quadro-neurologico.component';
import { SettingRiabilitativoComponent } from './setting-riabilitativo/setting-riabilitativo.component';
import { ValutazioneComponent } from './valutazione/valutazione.component';
import {ComplicanzeComponent} from "../../../../shared/components/complicanze/complicanze.component";
import { TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO } from '../../../../shared/utils/const';
import {ETipoScheda} from "../../../../shared/enums/enum";
import { CronologiaMenuSimpleComponent } from '../../../../shared/components/cronologia-menu-simple/cronologia-menu-simple.component';
import {OutputHistoryDTO} from "../../../../shared/interfaces/shared/shared.interface";
import {MatButton, MatIconButton} from "@angular/material/button";
import {MatIcon} from "@angular/material/icon";

@Component({
  selector: 'app-dati-clinici',
  standalone: true,
  imports: [
    CommonModule,
    MatExpansionModule,
    EziologiaComponent,
    LesioneTrattamentoComponent,
    ValutazioneComponent,
    QuadroNeurologicoComponent,
    SettingRiabilitativoComponent,
    NecessitaAssistenzialiComponent,
    ComplicanzeComponent,
    CronologiaMenuSimpleComponent,
    MatButton,
    MatIconButton,
    MatIcon
  ],
  templateUrl: './dati-clinici.component.html',
  styleUrl: './dati-clinici.component.scss'
})
export class DatiCliniciComponent implements OnInit, OnDestroy {
  @ViewChildren(MatExpansionPanel) panels: QueryList<MatExpansionPanel>;
  @ViewChildren(EziologiaComponent) eziologiaComponent: QueryList<EziologiaComponent>;
  @ViewChildren(LesioneTrattamentoComponent) lesioneComponent: QueryList<LesioneTrattamentoComponent>;
  @ViewChildren(ValutazioneComponent) valutazioneComponent: QueryList<ValutazioneComponent>;
  @ViewChildren(NecessitaAssistenzialiComponent) necessitaComponent: QueryList<NecessitaAssistenzialiComponent>;
  @ViewChildren(QuadroNeurologicoComponent) quadroComponent: QueryList<QuadroNeurologicoComponent>;
  @ViewChildren(SettingRiabilitativoComponent) settingComponent: QueryList<SettingRiabilitativoComponent>;
  @ViewChildren(ComplicanzeComponent) complicanzeComponent: QueryList<ComplicanzeComponent>;

  @Output() menuItemsChange = new EventEmitter<SceltaSchedaEnum[]>();

  datiClinici: SchedaDatiCliniciModel;
  idReparto: number;
  dataRicovero: string;
  menuItems = [SceltaSchedaEnum.GENERALI]
  tipoEvento: number;
  idPaziente: number;
  datiCronologia: OutputHistoryDTO | null = null;

  openEziologia = false;
  showEziologia = false;

  eziologiaReadOnly: boolean = false;
  lesioneReadOnly: boolean;
  valutazioneReadOnly: boolean = false;
  necessitaReadOnly: boolean;
  quadroReadOnly: boolean;
  settingReadOnly: boolean;
  complicanzeReadOnly: boolean;

  hasBeenOpenedEziologia: boolean = true;
  hasBeenOpenedLesione: boolean = false;
  hasBeenOpenedValutazione: boolean = false;
  hasBeenOpenedNecessita: boolean = false;
  hasBeenOpenedQuadro: boolean = false;
  hasBeenOpenedSetting: boolean = false;
  hasBeenOpenedComplicanze: boolean = false;

  protected readonly TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO = TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO;
  protected readonly ETipoScheda = ETipoScheda;

  private destroy$ = new Subject<void>();

  expandedPanels: { [key: string]: boolean } = {
    'eziologia': true,
    'lesione': false,
    'valutazione': false,
    'necessita': false,
    'quadro': false,
    'setting': false,
    'complicanze': false
  };
  private getCronologia$: Subscription;


  constructor(
    private schedaRicoveroService: SchedaRicoveroService,
    private operatoreService: OperatoreService
  ) { }

  ngOnInit(): void {
    this.schedaRicoveroService.schedaDettagliata$
      .pipe(takeUntil(this.destroy$))
      .subscribe((value: SchedaRicoveroModel | null) => {
        if (!value) return;
        this.tipoEvento = value.evento.tipoEvento.idTipoEvento;
        this.datiClinici = value.datiClinici;
        this.idReparto = value.scheda.unitaOperativa.idUnitaOperativa;
        this.dataRicovero = value.scheda.dataRicovero as string;
        this.eziologiaReadOnly = value.evento.tipoEvento.idTipoEvento === TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO.idTipoEvento || this.checkEnable(value, 'eziologia');
        this.valutazioneReadOnly = this.checkEnable(value, 'valutazione');
        this.lesioneReadOnly = value.evento.tipoEvento.idTipoEvento === TIPO_EVENTO_PRIMO_RICOVERO_RIABILITATIVO.idTipoEvento || this.checkEnable(value);
        this.necessitaReadOnly = this.checkEnable(value);
        this.quadroReadOnly = this.checkEnable(value);
        this.settingReadOnly = this.checkEnable(value);
        this.complicanzeReadOnly = this.checkEnable(value);
        this.idPaziente = value.idPaziente;
      });
  }

  onEziologiaReady() {
    this.openEziologia = true;
  }

  openEziologiaPanel() {
    this.showEziologia = true;       // fa montare il componente
    this.openEziologia = false;      // resetta il flag
  }

  // quando premo su info della modale
  onCronologiaDatiInfoClick = (event: OutputHistoryDTO) => {
    console.log(event)
    this.datiCronologia = event
  }

  onOpened(accordion: string) {

    switch (accordion) {
      case 'eziologia':
        this.hasBeenOpenedEziologia = true;
        break;
      case 'lesione':
        this.hasBeenOpenedLesione = true;
        break;
      case 'valutazione':
        this.hasBeenOpenedValutazione = true;
        break;
      case 'necessita':
        this.hasBeenOpenedNecessita = true;
        break;
      case 'quadro':
        this.hasBeenOpenedQuadro = true;
        break;
      case 'setting':
        this.hasBeenOpenedSetting = true;
        break;
      case 'complicanze':
        this.hasBeenOpenedComplicanze = true;
        break;

      default:
        break;
    }

  }

  checkEnable(value: SchedaRicoveroModel, field?: string): boolean {
    return value.evento.stato?.descrizione.toUpperCase() === 'CHIUSO'
    //|| ((field === 'eziologia' || field === 'valutazione') && this.operatoreService.getOperatore().descRuoloOperatore?.toUpperCase() !== 'AMMINISTRATIVO')
  }

    onShowDimissione(show: boolean) {
    if (show) {
      // Se show è true, aggiungiamo DIMISSIONI se non è già presente
        this.menuItems = [SceltaSchedaEnum.GENERALI, SceltaSchedaEnum.CLINICI, SceltaSchedaEnum.DIMISSIONI];
        this.menuItemsChange.emit(this.menuItems);
    } else {
      // Se show è false, rimuoviamo DIMISSIONI se è presente
        this.menuItems = [SceltaSchedaEnum.GENERALI, SceltaSchedaEnum.CLINICI];
        this.menuItemsChange.emit(this.menuItems);
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.getCronologia$?.unsubscribe()
  }
}
