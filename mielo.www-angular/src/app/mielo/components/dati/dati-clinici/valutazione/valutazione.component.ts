import { CommonModule } from '@angular/common';
import {Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges} from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from "@angular/forms";
import { distinctUntilChanged, forkJoin, Subscription } from "rxjs";
import { MaterialModule } from "../../../../../core/material.module";
import { SchedaValutazioneIngressoModel } from '../../../../../shared/interfaces/dati-clinici.interface';
import { DizionarioModel } from '../../../../../shared/interfaces/scheda-ricovero.interface';
import { CapitalizePipe } from '../../../../../shared/pipes/capitalize.pipe';
import { DecoderService } from '../../../../../shared/services/decoder.service';
import { ModalService } from '../../../../../shared/services/modal.service';
import {convertToDate, deepClone, disableAllFieldsInForm} from '../../../../../shared/utils/utils';
import { DatiCliniciService } from '../../../../services/dati-clinici.service';
import {SchedaValutazioneDimissioneModel} from "../../../../../shared/interfaces/dati-dimissione.interface";
import {DatiDimissioneService} from "../../../../services/dati-dimissione.service";
import { TipoRicoveroEnum } from '../../../../../shared/enums/tipo-ricovero.enum';
import {OutputHistoryDTO} from "../../../../../shared/interfaces/shared/shared.interface";


@Component({
  selector: 'app-valutazione',
  standalone: true,
  imports: [
    CommonModule,
    MaterialModule,
    ReactiveFormsModule,
    CapitalizePipe
  ],
  templateUrl: './valutazione.component.html',
  styleUrl: './valutazione.component.scss'
})
export class ValutazioneComponent implements OnInit, OnDestroy, OnChanges {

  @Input()
  isDimissione: boolean;

  @Input()
  readOnly: boolean = false;

  @Input()
  dataRicovero: string = '';

  @Input() 
  repartoType: TipoRicoveroEnum;

  @Input() cronologiaInfo: OutputHistoryDTO | null = null;

  isOltreSetteGiorni: boolean = false;

  formReady: boolean = false;
  datiValutazione: SchedaValutazioneIngressoModel | SchedaValutazioneDimissioneModel;

  optionsValutazione: Array<DizionarioModel>;
  optionsCollaborazione: Array<DizionarioModel>;

  valutazioneForm: FormGroup;
  savedFormBeforeCronologia: FormGroup

  get formGroup(): FormGroup {
    return this.valutazioneForm;
  }

  private subscriptions: Subscription[] = [];

  constructor(
    private fb: FormBuilder,
    private modalService: ModalService,
    public datiCliniciService: DatiCliniciService,
    private datiDimissioneService: DatiDimissioneService,
    private decoderService: DecoderService,
  ) {
    this.valutazioneForm = this.fb.group({
      vigilanza: [null, Validators.required],
      noteVigilanza: [null],
      collaborazione: [null, Validators.required],
      noteCollaborazione: [null],
      orientamentoSpaziale: [null, Validators.required],
      noteOrientamentoSpaziale: [null],
      orientamentoTemporale: [null, Validators.required],
      noteOrientamentoTemporale: [null],
      deficitCognitivo: [null, Validators.required],
      noteDeficitCognitivo: [null]
    });
  }

  ngOnInit() {

    forkJoin({
      valutazione: this.decoderService.getDcodSiNoNonValutabile(),
      collaborazione: this.decoderService.getDcodCollaborazione(),
    }).subscribe(options => {
      this.optionsValutazione = options.valutazione;
      this.optionsCollaborazione = options.collaborazione;

      let dati;

      if(!this.isDimissione){

        this.isOltreSetteGiorni = this.checkOltreSetteGiorni(convertToDate(this.dataRicovero));
        dati = this.datiCliniciService.getDatiValutazioneValue();
        
      } else if (this.isDimissione) {

        dati = this.datiDimissioneService.getValutazioneValue();

        if (this.repartoType === TipoRicoveroEnum.ACUTI) {
          Object.keys(this.valutazioneForm.controls).forEach(control => {
            this.valutazioneForm.get(control)?.removeValidators(Validators.required);
          })
        }

      }

      // Ora che le opzioni sono pronte, posso popolare il form
      if (dati) {
        this.datiValutazione = dati;
        this.populateForm(dati);
      }

      this.formReady = true;

      if (this.readOnly) {
        disableAllFieldsInForm(this.valutazioneForm)
      }

      this.valutazioneForm.updateValueAndValidity();
    });

    if (!this.readOnly) {
      const formChanges = this.valutazioneForm.valueChanges.pipe(
        distinctUntilChanged((a, b) => JSON.stringify(a) === JSON.stringify(b)),
      ).subscribe(() => {
        const formValue = this.valutazioneForm.getRawValue();
        this.savedFormBeforeCronologia = deepClone(this.valutazioneForm);

        const payload = {
          idScheda: this.datiValutazione.idScheda,
          nomeScheda: this.datiValutazione.nomeScheda,
          ...formValue,
        };

        this.valutazioneForm.updateValueAndValidity()
        if (!this.isDimissione) {
          this.datiCliniciService.setDatiValutazione(payload);
          this.datiCliniciService.setDatiValutazioneValido(this.valutazioneForm.valid);
        } else {
          this.datiDimissioneService.setValutazione(payload);
          this.datiDimissioneService.setValutazioneValido(this.valutazioneForm.valid);
        }
      })

      this.subscriptions.push(formChanges);
    }

  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['cronologiaInfo'].currentValue) {
      this.populateForm(changes['cronologiaInfo'].currentValue.schedaClinica as SchedaValutazioneIngressoModel)
      disableAllFieldsInForm(this.valutazioneForm)
    } else {
      this.valutazioneForm = this.savedFormBeforeCronologia;
      this.valutazioneForm.updateValueAndValidity()
    }
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
  }

  // Metodo per controllare la disabilitazione del SOLO pulsante NOTE, non del campo di testo interno 
  isNoteDisabled(controlName: string): boolean {
    const control = this.valutazioneForm.get(controlName);
    return !control?.value && !!control?.disabled;
  } 

  isRequired(controlName: string): boolean {
    const control = this.valutazioneForm.get(controlName);
    return this.datiCliniciService.isRequired(control);
  }

  checkOltreSetteGiorni(date: Date | null): boolean {
    if (!date) return false;

    const today = new Date();
    const setteGiorniDopo = new Date(date);
    setteGiorniDopo.setDate(setteGiorniDopo.getDate() + 7);

    return today > setteGiorniDopo;
  }


  populateForm(val: SchedaValutazioneIngressoModel | SchedaValutazioneDimissioneModel) {
    const findOption = (options: DizionarioModel[], diz: DizionarioModel | null | undefined): DizionarioModel | null => {
      if (!diz || !diz.idDizionario) return null;
      return options.find(opt => opt.idDizionario === diz.idDizionario) ?? null;
    };

    this.valutazioneForm.get('vigilanza')?.setValue(findOption(this.optionsValutazione, val.vigilanza), { emitEvent: false });
    this.valutazioneForm.get('collaborazione')?.setValue(findOption(this.optionsCollaborazione, val.collaborazione), { emitEvent: false });
    this.valutazioneForm.get('orientamentoSpaziale')?.setValue(findOption(this.optionsValutazione, val.orientamentoSpaziale), { emitEvent: false });
    this.valutazioneForm.get('orientamentoTemporale')?.setValue(findOption(this.optionsValutazione, val.orientamentoTemporale), { emitEvent: false });
    this.valutazioneForm.get('deficitCognitivo')?.setValue(findOption(this.optionsValutazione, val.deficitCognitivo), { emitEvent: false });

    this.valutazioneForm.get('noteVigilanza')?.setValue(val.noteVigilanza ?? null, { emitEvent: false });
    this.valutazioneForm.get('noteCollaborazione')?.setValue(val.noteCollaborazione ?? null, { emitEvent: false });
    this.valutazioneForm.get('noteOrientamentoSpaziale')?.setValue(val.noteOrientamentoSpaziale ?? null, { emitEvent: false });
    this.valutazioneForm.get('noteOrientamentoTemporale')?.setValue(val.noteOrientamentoTemporale ?? null, { emitEvent: false });
    this.valutazioneForm.get('noteDeficitCognitivo')?.setValue(val.noteDeficitCognitivo ?? null, { emitEvent: false });
  }


  openPopupNote(field: string) {
    const noteField = `note${field.charAt(0).toUpperCase() + field.slice(1)}`;
    const noteControl = this.valutazioneForm.get(noteField);

    if (noteControl) {
      const configNote = this.modalService.createNoteParam(noteControl.value, this.readOnly);

      this.modalService.note(configNote)?.subscribe((res) => {
        if (res != noteControl.value) {
          noteControl.setValue(res);
          noteControl.markAsDirty();
          noteControl.updateValueAndValidity();
        }
        this.datiCliniciService.adjustDialogHeight();
      });
    }
  }
}
